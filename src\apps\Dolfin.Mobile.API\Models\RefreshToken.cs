using System;

namespace Dolfin.Mobile.API.Models
{
    /// <summary>
    /// Represents a refresh token for JWT authentication
    /// </summary>
    public class RefreshToken
    {
        /// <summary>
        /// Unique identifier for the refresh token
        /// </summary>
        public string Token { get; set; }

        /// <summary>
        /// The JWT token ID this refresh token is associated with
        /// </summary>
        public string JwtId { get; set; }

        /// <summary>
        /// When the token was created
        /// </summary>
        public DateTime CreationDate { get; set; }

        /// <summary>
        /// When the token expires
        /// </summary>
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// Has this token been used
        /// </summary>
        public bool Used { get; set; }

        /// <summary>
        /// Has this token been invalidated
        /// </summary>
        public bool Invalidated { get; set; }

        /// <summary>
        /// The user ID this token belongs to
        /// </summary>
        public string UserId { get; set; }
    }
}
