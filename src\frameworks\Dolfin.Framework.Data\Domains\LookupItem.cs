using System;

namespace Dolfin.Framework.Data.Domains
{
    /// <summary>
    /// Represents an individual lookup item for dropdown lists
    /// </summary>
    public partial class LookupItem : _BaseDomain
    {
        /// <summary>
        /// Unique code for the lookup item
        /// </summary>
        public required string Code { get; set; }

        /// <summary>
        /// Display value for the lookup item
        /// </summary>
        public required string Value { get; set; }

        /// <summary>
        /// Optional description for the lookup item
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Display order for the lookup item
        /// </summary>
        public int? DisplayOrder { get; set; }

        /// <summary>
        /// Additional data in JSON format (can store custom properties)
        /// </summary>
        public string? AdditionalData { get; set; }

        /// <summary>
        /// Indicates if the lookup item is system-defined (cannot be modified by users)
        /// </summary>
        public bool IsSystem { get; set; }

        /// <summary>
        /// ID of the lookup group this item belongs to
        /// </summary>
        public Guid LookupGroupId { get; set; }

        /// <summary>
        /// Navigation property to the lookup group
        /// </summary>
        public virtual LookupGroup LookupGroup { get; set; }
    }
}
