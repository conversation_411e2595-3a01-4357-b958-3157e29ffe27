﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class MsicCategoryReference : _BaseDomain
    {
        public MsicCategoryReference()
        {
            Msic = new HashSet<Msic>();
        }
        public required string Code { get; set; }
        public required string Description { get; set; }
        public virtual ICollection<Msic> Msic { get; }
    }
}
