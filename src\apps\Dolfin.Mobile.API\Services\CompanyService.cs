﻿using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Common;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Linq.Expressions;
using static Dolfin.Utility.Constant.Constant;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Services
{
    public class CompanyService : BaseComponent<Company>, ICompanyService
    {
        private readonly StandardMessage _standardMessage;
        private readonly IAuthService _authService;
        private readonly ISettingsService _settingService;
        private readonly IUserService _userService;
        private readonly IInventoryService _inventoryService;
        private readonly IPrefixService _prefixService;
        //private readonly ICustomerService _customerService;
        private readonly IMapper _mapper;
        private readonly ICacheService _cacheService;
        private readonly CacheSettings _cacheSettings;
        private readonly ILogger<CompanyService> _logger;

        public CompanyService(
            DbContextOptions<DolfinDbContext> dbContextOptions,
            IAuthService authService,
            ISettingsService settingService,
            IUserService userService,
            IInventoryService inventoryService,
            IPrefixService prefixService,
            //ICustomerService customerService,
            IMapper mapper,
            ICacheService cacheService,
            IOptions<CacheSettings> cacheSettings,
            ILogger<CompanyService> logger) : base(dbContextOptions)
        {
            _standardMessage = new StandardMessage();
            _authService = authService;
            _settingService = settingService;
            _userService = userService;
            _inventoryService = inventoryService;
            _prefixService = prefixService;
            //_customerService = customerService;
            _mapper = mapper;
            _cacheService = cacheService;
            _cacheSettings = cacheSettings.Value;
            _logger = logger;
        }

        // companyId only for Admin use
        public async Task<BaseResponse<Company>> GetCompanyProfile(Guid? companyId)
        {
            var result = new BaseResponse<Company> { IsSuccessful = true };
            try
            {
                var response = await GetCompanyById(companyId);
                //if (response != null)
                //{
                //    await MassageCompanyProfile(response, loginUserType);
                //}
                result.Result = response;
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Company, Company>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        private async Task<Company> GetCompanyById(Guid? companyId)
        {
            var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync(companyId);
            var currentUser = getCurrentUser.Item1;
            companyId = getCurrentUser.Item2;

            // If companyId is provided, try to get from cache
            if (companyId.HasValue)
            {
                string cacheKey = CacheKeys.Company.GetCompanyKey(companyId.Value);

                return await _cacheService.GetOrCreateAsync(
                    cacheKey,
                    async () =>
                    {
                        _logger.LogInformation("Loading company {CompanyId} from database", companyId);
                        return await GetCompanyFromDatabase(companyId, currentUser);
                    },
                    _cacheSettings.DefaultAbsoluteExpirationMinutes);
            }

            // If no companyId, just get from database (no caching)
            return await GetCompanyFromDatabase(companyId, currentUser);
        }

        /// <summary>
        /// Gets company data directly from the database without caching
        /// </summary>
        private async Task<Company> GetCompanyFromDatabase(Guid? companyId, ApplicationUser currentUser)
        {
            var query = GetDbContext().Set<Company>().AsQueryable()
                .Include(x => x.DefaultSalesTaxNo).ThenInclude(x => x.TaxCategory)//.ThenInclude(x => x.TaxRate.Where(tr => tr.TaxCategoryId == x.Id))
                .Include(x => x.DefaultServiceTaxNo).ThenInclude(x => x.TaxCategory)//.ThenInclude(x => x.TaxRate.Where(tr => tr.TaxCategoryId == x.Id))
                .Include(x => x.Currency)
                .Include(x => x.TaxCategory)
                .Include(x => x.Msic).ThenInclude(x => x.MsicCategoryReference)
                .Include(x => x.User.Where(u => u.IsActive))
                .Include(x => x.Branch.Where(b => b.IsActive))
                .Where(x => (companyId == null || x.Id == companyId) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                .Where(x => x.DefaultSalesTaxNo == null || x.DefaultSalesTaxNo.IsActive)
                .Where(x => x.DefaultSalesTaxNo == null || x.DefaultSalesTaxNo.TaxCategory == null || x.DefaultSalesTaxNo.TaxCategory.IsActive)
                .Where(x => x.DefaultServiceTaxNo == null || x.DefaultServiceTaxNo.IsActive)
                .Where(x => x.DefaultServiceTaxNo == null || x.DefaultServiceTaxNo.TaxCategory == null || x.DefaultServiceTaxNo.TaxCategory.IsActive)
                .Where(x => x.Currency.IsActive)
                .Where(x => x.TaxCategory.IsActive)
                .Where(x => x.Msic == null || x.Msic.IsActive)
                .Where(x => x.Msic == null || x.Msic.MsicCategoryReference == null || x.Msic.MsicCategoryReference.IsActive);

            return await query.FirstOrDefaultAsync();
        }

        /// <summary>InvalidateCompanyCache
        /// Invalidates the cache for a specific company
        /// </summary>
        /// <param name="companyId">The company ID</param>
        public void InvalidateCompanyCache(Guid companyId)
        {
            string cacheKey = CacheKeys.Company.GetCompanyKey(companyId);
            _cacheService.Remove(cacheKey);
            _logger.LogInformation("Cache invalidated for company {CompanyId} using key {CacheKey}",
                companyId, cacheKey);
        }

        public async Task<BaseResponse<Company>> GetCompanyByUserId(Guid? userId)
        {
            var result = new BaseResponse<Company> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser == null)
                    throw new Exception(StandardErrorMessage.AccessError);
                else if (currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
                    userId = Guid.Parse(currentUser.Id); // only allow to get own company profile

                var query = GetDbContext().Set<Company>().AsQueryable()
                    .Include(x => x.DefaultSalesTaxNo).ThenInclude(x => x.TaxCategory)
                    .Include(x => x.DefaultServiceTaxNo).ThenInclude(x => x.TaxCategory)
                    .Include(x => x.Currency)
                    .Include(x => x.TaxCategory)
                    .Include(x => x.Msic).ThenInclude(x => x.MsicCategoryReference)
                    //.Include(x => x.User)
                    .Where(x => x.User.Any(x => x.Id == userId.ToString()) && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(x => x.DefaultSalesTaxNo == null || x.DefaultSalesTaxNo.IsActive)
                    .Where(x => x.DefaultSalesTaxNo == null || x.DefaultSalesTaxNo.TaxCategory == null || x.DefaultSalesTaxNo.TaxCategory.IsActive)
                    .Where(x => x.DefaultServiceTaxNo == null || x.DefaultServiceTaxNo.IsActive)
                    .Where(x => x.DefaultServiceTaxNo == null || x.DefaultServiceTaxNo.TaxCategory == null || x.DefaultServiceTaxNo.TaxCategory.IsActive)
                    .Where(x => x.Currency.IsActive)
                    .Where(x => x.TaxCategory.IsActive)
                    .Where(x => x.Msic == null || x.Msic.IsActive)
                    .Where(x => x.Msic == null || x.Msic.MsicCategoryReference == null || x.Msic.MsicCategoryReference.IsActive);

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Company, Company>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        //Only Admin allow to add for company
        public async Task<BaseResponse<ResultId>> InsertCompany(CompanyRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var existingUser = await _userService.GetUserByUsername(reqBody.UserRequest.Username);
                    if (existingUser != null)
                        throw new Exception("Username duplicated.");

                    var subscriptionByGuid = await GetSubscriptionByGuid(reqBody.SubscriptionId);
                    if (subscriptionByGuid == null || !subscriptionByGuid.IsSuccessful || subscriptionByGuid.Result?.Months == null)
                        throw new Exception("Subscription not found.");

                    var currentUser = await _userService.GetCurrentUserStdUpsertByAdminAsync();

                    await CompanyValidation(reqBody.RegNo, reqBody.SstNo, reqBody.TinNo);

                    // Address
                    reqBody.AddressRequest.Company = reqBody.Name;
                    reqBody.AddressRequest.Email = reqBody.UserRequest.Email;
                    reqBody.AddressRequest.PhoneNo = reqBody.UserRequest.PhoneNo1;
                    reqBody.AddressRequest.FaxNo = reqBody.UserRequest.FaxNo1;

                    var currentDateTime = DateTime.UtcNow;
                    reqBody.ExpiredAt = currentDateTime.AddMonths(subscriptionByGuid.Result.Months);
                    var addressResponse = await _settingService.InsertAddress(reqBody.AddressRequest, dbContextRollback: dbContext);
                    if (!addressResponse.IsSuccessful)
                        throw new Exception(addressResponse.Exception);

                    // Company
                    var newCompany = _mapper.Map<Company>(reqBody);
                    newCompany.IsActive = true;
                    newCompany.CreatedAt = DateTime.UtcNow;
                    newCompany.CreatedBy = Guid.Parse(currentUser.Id);
                    var company = await CreateAsync(newCompany, dbContext);

                    // Branch
                    var newBranch = new BranchRequest()
                    {
                        Code = newCompany.Code,
                        Name = newCompany.Name,
                        CompanyId = company.Id,
                        IsHq = true,
                        AddressId = addressResponse.Result.Id.Value
                    };
                    var branch = await InsertBranch(newBranch, Guid.Parse(currentUser.Id), dbContext);
                    if (!branch.IsSuccessful)
                        throw new Exception(branch.Exception);

                    var newInventory = new InventoryRequest()
                    {
                        Code = reqBody.Code,
                        Name = reqBody.Name,
                        BranchId = (Guid)branch.Result.Id.Value
                    };
                    var inventory = await _inventoryService.InsertInventory(newInventory, dbContext);

                    // User
                    reqBody.UserRequest.UserTypeId = UserTypeEnum.SuperAdmin.GetAmbientValue().Value;

                    var newRegister = _mapper.Map<Register>(reqBody.UserRequest);
                    var (userResponse, newUserId) = await _authService.RegisterAsync(newRegister, Guid.Parse(currentUser.Id), dbContext);
                    if (!userResponse.IsSuccessful)
                        throw new Exception(userResponse.Exception);

					if (currentTransaction == null)
						await transaction.CommitAsync();

					// after created the branch and company data only update branch and company relationship in user table
					newRegister.BranchId = (Guid)branch.Result.Id;
					newRegister.CompanyId = company.Id;
                    await _authService.UpdateAsync(newRegister, newUserId, dbContext);

                    // Create default "Cash" customer for the company
                    //await CreateDefaultCashCustomer(
                    //    company.Id,
                    //    (Guid)branch.Result.Id.Value,
                    //    company.CurrencyId,
                    //    Guid.Parse(currentUser.Id),
                    //    dbContext);

                    // Invalidate company cache after successful creation
                    InvalidateCompanyCache(company.Id);
                    _logger.LogInformation("Invalidated cache for newly created company {CompanyId}", company.Id);

                    result.Result = new ResultId { Id = company.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<PagedList<Branch>>> GetBranchList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null)
        {
            var result = new BaseResponse<PagedList<Branch>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync(companyId);
                var currentUser = getCurrentUser.Item1;
                companyId = getCurrentUser.Item2;

                Expression<Func<Branch, bool>> predicate = x => (x.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<Branch>()
                    .Include(u => u.Address) // Include Address to prevent lazy loading and circular reference
                    .Include(u => u.Company).ThenInclude(u => u.DefaultSalesTaxNo)
                    .Include(u => u.Company).ThenInclude(u => u.DefaultServiceTaxNo)
                    .Where(predicate)
                    .Where(u => u.Address == null || u.Address.IsActive)
                    .Where(u => u.Company.IsActive)
                    .Where(u => u.Company.DefaultSalesTaxNo == null || u.Company.DefaultSalesTaxNo.IsActive)
                    .Where(u => u.Company.DefaultServiceTaxNo == null || u.Company.DefaultServiceTaxNo.IsActive)
                    .AsQueryable();

                var response = await query.ToListAsync();
                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<Branch>, PagedList<Branch>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<Branch>> GetBranchByGuid(Guid branchId)
        {
            var result = new BaseResponse<Branch> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = currentUser.CompanyId;

                var query = GetDbContext().Set<Branch>()
                    .Include(u => u.Address) // Include Address to prevent lazy loading and circular reference
                    .Include(u => u.Company)
                    .Include(u => u.Company).ThenInclude(u => u.DefaultSalesTaxNo)
                    .Include(u => u.Company).ThenInclude(u => u.DefaultServiceTaxNo)
                    .Where(x => x.Id == branchId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(x => x.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(u => u.Address == null || u.Address.IsActive)
                    .Where(u => u.Company.IsActive)
                    .Where(u => u.Company.DefaultSalesTaxNo == null || u.Company.DefaultSalesTaxNo.IsActive)
                    .Where(u => u.Company.DefaultServiceTaxNo == null || u.Company.DefaultServiceTaxNo.IsActive)
                    .AsQueryable();
                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Branch, Branch>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateBranch(UpdateBranchRequest reqBody, Guid? userId = null, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync();
                    if (currentUser == null || (currentUser != null && currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString()))))
                        throw new Exception(StandardErrorMessage.AccessError);
                    else if (currentUser.UserRoles.Any(x => x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
                    {
                        if (reqBody.CompanyId == null)
                            throw new Exception("Company Id is mandatory field for Admin user.");
                        if (reqBody.Id == null)
                            throw new Exception("Branch Id is mandatory field for Admin user.");
                    }
                    else
                    {
                        // others than Admin, only allow get company Id and Branch Id from login user
                        reqBody.CompanyId = currentUser.CompanyId;
                        reqBody.Id = (Guid)currentUser.BranchId;
                    }

                    if (userId == null)
                        userId = Guid.Parse(currentUser.Id);

                    if (reqBody.Id == null)
                        throw new Exception("Branch Id is required.");

                    var branchList = await GetBranchList(reqBody.CompanyId, reqBody.Id);

                    // Branch
                    if (branchList.Count == 0)
                        throw new Exception("Branch Id not found.");

                    var updateBranch = _mapper.Map<Branch>(branchList.Find(x => x.Id == (Guid)reqBody.Id));
                    if (reqBody.IsHq)
                        await IsHqBranchUpdateToFalse((Guid)reqBody.CompanyId, userId);
                    if (currentUser.UserRoles.Any(x => x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
                    {
                        updateBranch.Name = reqBody.Name;
                        updateBranch.AddressId = reqBody.AddressId;
                    }

                    updateBranch.IsHq = true;
                    updateBranch.UpdatedAt = DateTime.UtcNow;
                    updateBranch.UpdatedBy = (Guid)userId;
                    await UpdateAsync(updateBranch, dbContext);

                    if (currentTransaction == null)
                        await transaction.CommitAsync();

                    // Invalidate company cache after branch update since branch data is included in company
                    if (reqBody.CompanyId.HasValue)
                    {
                        InvalidateCompanyCache(reqBody.CompanyId.Value);
                        _logger.LogInformation("Invalidated cache for company {CompanyId} after branch update", reqBody.CompanyId.Value);
                    }

                    result.Result = new ResultId { Id = updateBranch.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertBranch(BranchRequest reqBody, Guid? userId = null, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    if (userId == null)
                        userId = Guid.Parse(currentUser.Id);

                    // Check existing Is HQ - only one HQ for each company
                    if (reqBody.IsHq)
                        await IsHqBranchUpdateToFalse((Guid)reqBody.CompanyId, userId);

                    var newBranch = _mapper.Map<Branch>(reqBody);
                    newBranch.IsActive = true;
                    newBranch.CreatedAt = DateTime.UtcNow;
                    newBranch.CreatedBy = (Guid)userId;
                    var branch = await CreateAsync<Branch>(newBranch, dbContext);

                    if (currentTransaction == null)
                        await transaction.CommitAsync();

                    // Invalidate company cache after branch creation since branch data is included in company
                    if (reqBody.CompanyId != Guid.Empty)
                    {
                        InvalidateCompanyCache(reqBody.CompanyId);
                        _logger.LogInformation("Invalidated cache for company {CompanyId} after branch creation", reqBody.CompanyId);
                    }

                    result.Result = new ResultId { Id = branch.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<PagedList<TaxCategories>>> GetTaxCategoryList(Pagination pagination = null)
        {
            var result = new BaseResponse<PagedList<TaxCategories>> { IsSuccessful = true };
            try
            {
                UserTypeEnum? userType = null;
                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser != null)
                    userType = Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId));

                var response = await GetTaxCategoryList(userType);
                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<TaxCategories>, PagedList<TaxCategories>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<TaxCategories>> GetTaxCategoryByGuid(Guid taxCategoryId)
        {
            var result = new BaseResponse<TaxCategories> { IsSuccessful = true };
            try
            {

                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var branchId = currentUser.BranchId;

                var query = GetDbContext().Set<TaxCategories>().AsQueryable()
                    .Include(x => x.TaxRate.Where(r => r.IsActive))
                    .Where(x => x.Id == taxCategoryId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<TaxCategories, TaxCategories>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<PagedList<Subscription>>> GetSubscriptionList(Pagination pagination = null, CommonFilterList filterList = null)
        {
            var result = new BaseResponse<PagedList<Subscription>> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();

                Expression<Func<Subscription, bool>> predicate = x => (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<Subscription>()
                    .Where(predicate)
                    .AsQueryable();

                var response = await query.ToListAsync();
                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<Subscription>, PagedList<Subscription>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<Subscription>> GetSubscriptionByGuid(Guid subscriptionId)
        {
            var result = new BaseResponse<Subscription> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = currentUser.CompanyId;

                var query = GetDbContext().Set<Subscription>()
                    .Where(x => x.Id == subscriptionId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .AsQueryable();
                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Subscription, Subscription>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<TaxRate>> GetTaxRateByGuid(Guid taxRateId)
        {
            var result = new BaseResponse<TaxRate> { IsSuccessful = true };
            try
            {

                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var branchId = currentUser.BranchId;

                var query = GetDbContext().Set<TaxRate>().AsQueryable()
                    .Where(x => x.Id == taxRateId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<TaxRate, TaxRate>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertTaxRate(TaxRateRequest reqBody)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserStdUpsertByAdminAsync();

                // Tax Rate
                var newTaxRate = _mapper.Map<TaxRate>(reqBody);
                newTaxRate.IsActive = true;
                newTaxRate.CreatedAt = DateTime.UtcNow;
                newTaxRate.CreatedBy = Guid.Parse(currentUser.Id);

                var taxRateResponse = await CreateAsync(newTaxRate);
                result.Result = new ResultId { Id = taxRateResponse.Id };
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertTaxCategory(TaxCategoryRequest reqBody)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserStdUpsertByAdminAsync();

                await TaxCategoryValidation(reqBody.Code);

                // Tax Category
                var newTaxCategory = _mapper.Map<TaxCategories>(reqBody);
                newTaxCategory.IsActive = true;
                newTaxCategory.CreatedAt = DateTime.UtcNow;
                newTaxCategory.CreatedBy = Guid.Parse(currentUser.Id);

                var taxCategory = await CreateAsync(newTaxCategory);

                // Tax Rate
                //if (reqBody.TaxRateRequest != null && reqBody.TaxRateRequest.Count > 0)
                //{
                //    foreach (var taxRateRequest in reqBody.TaxRateRequest)
                //    {
                //        taxRateRequest.TaxCategoryId = taxCategory.Id;
                //        var userResponse = await InsertTaxRate(taxRateRequest);
                //        if (!userResponse.IsSuccessful)
                //        {
                //            throw new Exception(userResponse.Exception);
                //        }
                //    }
                //}
                result.Result = new ResultId { Id = taxCategory.Id };
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        private async Task<List<Branch>> GetBranchList(Guid? companyId, Guid? branchId)
        {
            var query = GetDbContext().Set<Branch>().AsQueryable()
                .Where(x => x.IsActive && (x.User.Any(u => (companyId == null || u.CompanyId == companyId) && (branchId == null || u.BranchId == branchId))));

            return await query.ToListAsync();
        }

        private async Task<bool> TaxCategoryValidation(string code)
        {
            var isValid = true;

            var query = GetDbContext().Set<TaxCategories>().AsQueryable()
                .Where(x => x.Code == code);

            var taxCategoriesResult = await query.ToListAsync();
            if (taxCategoriesResult.Any())
                throw new Exception($"Code duplicated found. Unique code is required.");

            return isValid;
        }

        private async Task<bool> CompanyValidation(string regNo, string sstNo, string tinNo)
        {
            var isValid = true;

            var query = GetDbContext().Set<Company>().AsQueryable()
                .Where(x => x.RegNo == regNo || x.SstNo == sstNo || x.TinNo == tinNo);

            var companyResult = await query.ToListAsync();
            if (companyResult.Any())
            {
                var errorMessage = "";
                isValid = false;
                if (companyResult.Where(x => x.RegNo == regNo).Count() > 0)
                    errorMessage += "Registation No, ";
                if (companyResult.Where(x => x.SstNo == sstNo).Count() > 0)
                    errorMessage += "SST No, ";
                if (companyResult.Where(x => x.TinNo == tinNo).Count() > 0)
                    errorMessage += "Tin No, ";

                throw new Exception($"{errorMessage.Substring(0, (errorMessage.Length - 2))} duplicated found");
            }

            return isValid;
        }

        private async Task<List<TaxCategories>> GetTaxCategoryList(UserTypeEnum? userType)
        {
            var query = GetDbContext().Set<TaxCategories>().AsQueryable()
                .Include(x => x.TaxRate.Where(r => r.IsActive))
                .Where(x => x.IsActive || (userType != null && SharedFunctionHelper.PermissionViewAll((UserTypeEnum)userType)));

            return await query.ToListAsync();
        }

        private async Task IsHqBranchUpdateToFalse(Guid companyId, Guid? userId)
        {
            try
            {
                var branchList = await GetBranchList(companyId, null);

                // Branch
                if (branchList.Count != 0)
                {
                    Branch? branch = null;
                    var existingHqBranch = branchList.Find(x => x.IsHq);
                    if (existingHqBranch != null)
                    {
                        existingHqBranch.IsHq = false;
                        existingHqBranch.UpdatedAt = DateTime.UtcNow;
                        existingHqBranch.UpdatedBy = userId ?? AdminConfiguration.SystemUser;
                        branch = await UpdateAsync(existingHqBranch);
                    }
                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }



        public async Task<BaseResponse<ResultId>> UpdateCompany(UpdateCompanyRequest reqBody, Guid? userId = null, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var currentUser = await _userService.GetCurrentUserStdUpsertByAdminAsync();

                    if (userId == null)
                        userId = Guid.Parse(currentUser.Id);

                    if (reqBody.Id == Guid.Empty)
                        throw new Exception("Company Id is required.");

                    // Get existing company
                    var existingCompany = await GetDbContext().Set<Company>()
                        .Where(x => x.Id == reqBody.Id && x.IsActive)
                        .FirstOrDefaultAsync();

                    if (existingCompany == null)
                        throw new Exception("Company not found.");

                    // Validate tax category (required)
                    var taxCategoryResponse = await GetTaxCategoryByGuid(reqBody.TaxCategoryId);
                    if (!taxCategoryResponse.IsSuccessful || taxCategoryResponse.Result == null)
                        throw new Exception("Invalid Tax Category ID.");

                    // Update only non-null properties using reflection
                    var properties = typeof(UpdateCompanyRequest).GetProperties()
                        .Where(p => p.Name != "Id" && p.CanRead);

                    foreach (var property in properties)
                    {
                        var value = property.GetValue(reqBody);
                        if (value != null)
                        {
                            var companyProperty = typeof(Company).GetProperty(property.Name);
                            if (companyProperty != null && companyProperty.CanWrite)
                            {
                                companyProperty.SetValue(existingCompany, value);
                            }
                        }
                    }

                    existingCompany.UpdatedAt = DateTime.UtcNow;
                    existingCompany.UpdatedBy = (Guid)userId;

                    await UpdateAsync(existingCompany, dbContext);

                    if (currentTransaction == null)
                        await transaction.CommitAsync();

                    // Invalidate company cache after update
                    InvalidateCompanyCache(reqBody.Id);
                    _logger.LogInformation("Invalidated cache for company {CompanyId} after update", reqBody.Id);

                    result.Result = new ResultId { Id = existingCompany.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    throw ex;
                }
                finally
                {
                    if (isNewDbContext)
                        dbContext.Dispose();
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        /// <summary>
        /// Creates a default "Cash" customer for a company
        /// </summary>
        /// <param name="companyId">The company ID</param>
        /// <param name="branchId">The branch ID</param>
        /// <param name="currencyId">The currency ID</param>
        /// <param name="userId">The user ID</param>
        /// <param name="dbContext">Optional database context for transaction support</param>
        /// <returns>The result of the customer creation operation</returns>
        //private async Task<BaseResponse<ResultId>> CreateDefaultCashCustomer(
        //    Guid companyId,
        //    Guid branchId,
        //    Guid currencyId,
        //    Guid userId,
        //    DolfinDbContext dbContext = null)
        //{
        //    try
        //    {
        //        _logger.LogInformation("Creating default Cash customer for company {CompanyId}", companyId);

        //        // Create the default Cash customer request
        //        var customerRequest = new CustomerRequest
        //        {
        //            Name = "Cash",
        //            Description = "cash",
        //            IsTaxExempt = false,
        //            IsPICEditable = true,
        //            DefaultPIC = "cash",
        //            AccountCode = "A00001",
        //            DebtorTypeId = new Guid("fa761c4b-5b44-47e2-bd6f-fffb1f5bbd98"), // Use the provided DebtorTypeId
        //            CurrencyId = currencyId,
        //            CompanyId = companyId,
        //            Address = null // No address for Cash customer
        //        };

        //        // Call the customer service to create the customer
        //        var result = await _customerService.InsertCustomer(customerRequest, dbContext);

        //        if (result.IsSuccessful)
        //        {
        //            _logger.LogInformation("Successfully created default Cash customer with ID {CustomerId} for company {CompanyId}",
        //                result.Result.Id, companyId);
        //        }
        //        else
        //        {
        //            _logger.LogError("Failed to create default Cash customer for company {CompanyId}: {ErrorMessage}",
        //                companyId, result.Exception);
        //        }

        //        return result;
        //    }
        //    catch (Exception ex)
        //    {
        //        _logger.LogError(ex, "Error creating default Cash customer for company {CompanyId}", companyId);
        //        var result = new BaseResponse<ResultId> { IsSuccessful = false };
        //        return _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
        //    }
        //}

        public async Task<BaseResponse<PagedList<Prefix>>> GetPrefixList(Pagination pagination = null, CommonFilterList filterList = null, Guid? branchId = null)
        {
            return await _prefixService.GetPrefixList(pagination, filterList, branchId);
        }

        public async Task<BaseResponse<Prefix>> GetPrefixByGuid(Guid prefixId)
        {
            return await _prefixService.GetPrefixByGuid(prefixId);
        }

        public async Task<BaseResponse<ResultId>> InsertPrefix(PrefixRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            return await _prefixService.InsertPrefix(reqBody, dbContextRollback);
        }

        public async Task<BaseResponse<ResultId>> UpdatePrefix(UpdatePrefixRequest reqBody)
        {
            return await _prefixService.UpdatePrefix(reqBody);
        }

        public async Task<NoResultResponse> DeletePrefix(Guid id)
        {
            return await _prefixService.DeletePrefix(id);
        }

        public async Task<BaseResponse<PagedList<Msic>>> GetMsicList(Pagination pagination = null, CommonFilterList filterList = null)
        {
            var result = new BaseResponse<PagedList<Msic>> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();

                // Apply the main predicate at the database level
                Expression<Func<Msic, bool>> predicate = x => x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<Msic>()
                    .Include(x => x.MsicCategoryReference)
                    .Where(predicate)
                    .Where(x => x.MsicCategoryReference == null || x.MsicCategoryReference.IsActive)
                    .AsQueryable();

                var response = await query.ToListAsync();

                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<Msic>, PagedList<Msic>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
        //#endregion
    }
}
