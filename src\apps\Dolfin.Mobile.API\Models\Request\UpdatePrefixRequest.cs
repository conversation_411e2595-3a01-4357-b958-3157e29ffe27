using System.ComponentModel.DataAnnotations;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Request model for updating an existing prefix
    /// </summary>
    public class UpdatePrefixRequest
    {
        /// <summary>
        /// The ID of the prefix to update
        /// </summary>
        [Required]
        public Guid Id { get; set; }

        /// <summary>
        /// The prefix value to use (e.g., "CUST" for customers)
        /// </summary>
        [Required]
        public required string PrefixValue { get; set; }

        public Guid? BranchId { get; set; }
    }
}
