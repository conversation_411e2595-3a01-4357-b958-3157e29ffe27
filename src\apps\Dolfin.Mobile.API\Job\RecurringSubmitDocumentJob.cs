﻿﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System;
using System.Threading;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Scheduler
{
    /// <summary>
    /// Recurring job to automatically submit eInvoice documents to LHDN
    /// </summary>
    internal class RecurringSubmitDocumentJob : BaseRecurringJob
    {
        private readonly RecurringSubmitDocumentJobSetting _jobSetting;
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;

        public RecurringSubmitDocumentJob(
            ILogger<RecurringSubmitDocumentJob> logger,
            IOptions<RecurringSubmitDocumentJobSetting> jobSetting,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _jobSetting = jobSetting.Value;
            _serviceProvider = serviceProvider;
        }

        public override void Execute()
        {
            try
            {
                _logger.LogInformation("[{JobName}] Job Started at {StartTime}", this.Name, DateTime.Now);
                _logger.LogInformation("[{JobName}] Using cron expression: {CronExpression}", this.Name, _jobSetting.CronExpression);

                // Process pending eInvoices
                ProcessPendingEInvoices().Wait();

                _logger.LogInformation("[{JobName}] Job Completed at {EndTime}", this.Name, DateTime.Now);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"[{this.Name}] Error executing recurring job: {ex.Message}");
                // Continue execution despite errors - don't rethrow
            }
        }

        private async Task ProcessPendingEInvoices()
        {
            // Create a scope to resolve scoped services
            using var scope = _serviceProvider.CreateScope();
            var scopedProvider = scope.ServiceProvider;

            // Get required services from the scoped provider
            var dbContext = scopedProvider.GetRequiredService<DolfinDbContext>();
            var eInvoiceService = scopedProvider.GetRequiredService<IEinvoiceService>();

            try
            {
                // Get pending eInvoices that haven't been submitted yet
                var pendingEInvoices = await dbContext.Set<EInvoice>()
                    .Include(e => e.Company)
                    .Where(e => e.Status == EInvoiceStatusEnum.Pending &&
                               (e.RetryCount ?? 0) < _jobSetting.MaxRetries &&
                               e.IsActive)
                    .OrderBy(e => e.CreatedAt)
                    .Take(_jobSetting.BatchSize)
                    .ToListAsync();

                _logger.LogInformation("Found {Count} pending eInvoices to process", pendingEInvoices?.Count ?? 0);

                foreach (var eInvoice in pendingEInvoices)
                {
                    try
                    {
                        _logger.LogInformation("Processing eInvoice {EInvoiceId} for invoice {InvoiceNo}", eInvoice.Id, eInvoice.InvoiceNo);

                        // Get company token if needed
                        await EnsureCompanyHasValidToken(eInvoice.Company, eInvoiceService);

                        // Submit the document
                        var submitRequest = new EInvoiceSubmitDocumentRequest
                        {
                            TrxId = eInvoice.TransactionId,
                            DocumentType = eInvoice.DocumentType,
                            DocumentTypeVersion = eInvoice.DocumentTypeVersion
                        };

                        var result = await eInvoiceService.SubmitDocument(submitRequest);

                        if (result.IsSuccessful && result.Result != null)
                        {
                            // Update eInvoice with response data
                            eInvoice.Status = EInvoiceStatusEnum.Submitted;
                            eInvoice.SubmissionUID = result.Result.SubmissionUID;
                            eInvoice.DocumentUUID = result.Result.DocumentUUID;
                            eInvoice.DocumentLongId = result.Result.DocumentLongId;
                            eInvoice.DocumentToken = result.Result.DocumentToken;
                            eInvoice.UpdatedAt = DateTime.UtcNow;

                            _logger.LogInformation("Successfully submitted eInvoice {EInvoiceId}", eInvoice.Id);
                        }
                        else
                        {
                            // Update retry count
                            eInvoice.RetryCount = (eInvoice.RetryCount ?? 0) + 1;
                            eInvoice.ResponseCode = result.StatusCode?.ToString();
                            eInvoice.ResponseMessage = result.StatusMessage;
                            eInvoice.UpdatedAt = DateTime.UtcNow;

                            _logger.LogWarning("Failed to submit eInvoice {EInvoiceId}: {StatusMessage}", eInvoice.Id, result.StatusMessage);
                        }

                        await dbContext.SaveChangesAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing eInvoice {EInvoiceId}: {ErrorMessage}", eInvoice.Id, ex.Message);

                        // Update retry count
                        eInvoice.RetryCount = (eInvoice.RetryCount ?? 0) + 1;
                        eInvoice.ResponseCode = "500";
                        eInvoice.ResponseMessage = ex.Message;
                        eInvoice.UpdatedAt = DateTime.UtcNow;

                        await dbContext.SaveChangesAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving pending eInvoices: {ErrorMessage}", ex.Message);
            }
        }

        private async Task EnsureCompanyHasValidToken(Company company, IEinvoiceService eInvoiceService)
        {
            if (company?.EInvoiceTokenDateTime == null || company.EInvoiceTokenDateTime <= DateTime.UtcNow)
            {
                _logger.LogInformation("Getting new token for company {CompanyName}", company.Name);

                var authRequest = new EInvoiceAuthRequest
                {
                    ClientId = company.EInvoiceClientId,
                    ClientSecret = company.EInvoiceClientSecret
                };

                var loginResponse = await eInvoiceService.LoginTaxpayerSystem(authRequest);

                if (!loginResponse.IsSuccessful)
                {
                    throw new Exception($"Failed to get token for company {company.Name}: {loginResponse.StatusMessage ?? "Unknown error"}");
                }

                _logger.LogInformation("Successfully obtained new token for company {CompanyName}", company.Name);
            }
        }
    }
}
