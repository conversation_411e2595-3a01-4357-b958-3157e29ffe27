﻿using Dolfin.Framework.Data.Domains;
using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models.Request
{
    public class CompanyRequest
    {
        public required string Code { get; set; }
        public required string Name { get; set; }
        public required string RegNo { get; set; }
        public required string SstNo { get; set; }
        public Guid? DefaultSalesTaxNoId { get; set; }
        public Guid? DefaultServiceTaxNoId { get; set; }
        public required string TinNo { get; set; }
        public string? Logo { get; set; }
        public bool IsBranchSameProduct { get; set; }
        public bool IsEnableInventory { get; set; }
        public bool IsRoundingAdjustment { get; set; }
        public bool IsEInvoiceSubmitAuto { get; set; } = false;
        public int? ConsolidatePay { get; set; }
        public string? WebSiteUrl { get; set; }
        public string? EInvoiceClientId { get; set; }
        public string? EInvoiceClientSecret { get; set; }
        public Guid CurrencyId { get; set; }
        public required Guid TaxCategoryId { get; set; }
        public Guid SubscriptionId { get; set; }
        public Guid? MsicId { get; set; }
        [JsonIgnore]
        public DateTime ExpiredAt { get; set; }
        public AddressCompanyRequest AddressRequest { get; set; }
        public UserRequest UserRequest { get; set; }
    }
}
