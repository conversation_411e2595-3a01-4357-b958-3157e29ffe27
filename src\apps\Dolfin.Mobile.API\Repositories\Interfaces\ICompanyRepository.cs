using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Repository.Interfaces;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories.Interfaces
{
    /// <summary>
    /// Repository interface for Company entity
    /// </summary>
    public interface ICompanyRepository : IRepository<Company>
    {
        /// <summary>
        /// Get company with all related entities
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Company with all related entities</returns>
        Task<Company> GetCompanyWithDetailsAsync(Guid companyId);
        
        /// <summary>
        /// Get company by user ID
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Company associated with the user</returns>
        Task<Company> GetCompanyByUserIdAsync(string userId);
        
        /// <summary>
        /// Get branches for a company
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>List of branches for the company</returns>
        Task<IEnumerable<Branch>> GetBranchesByCompanyIdAsync(Guid companyId);
        
        /// <summary>
        /// Get branch with details
        /// </summary>
        /// <param name="branchId">Branch ID</param>
        /// <returns>Branch with details</returns>
        Task<Branch> GetBranchWithDetailsAsync(Guid branchId);
        
        /// <summary>
        /// Invalidate company cache
        /// </summary>
        /// <param name="companyId">Company ID</param>
        void InvalidateCompanyCache(Guid companyId);
    }
}
