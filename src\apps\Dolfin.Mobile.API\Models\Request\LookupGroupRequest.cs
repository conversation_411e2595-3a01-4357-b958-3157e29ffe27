using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Request model for creating a new lookup group
    /// </summary>
    public class LookupGroupRequest
    {
        /// <summary>
        /// Unique code for the lookup group
        /// </summary>
        [Required]
        [StringLength(50)]
        public string Code { get; set; }

        /// <summary>
        /// Display name for the lookup group
        /// </summary>
        [Required]
        [StringLength(100)]
        public string Name { get; set; }

        /// <summary>
        /// Optional description for the lookup group
        /// </summary>
        [StringLength(500)]
        public string? Description { get; set; }

        /// <summary>
        /// Display order for the lookup group
        /// </summary>
        public int? DisplayOrder { get; set; }

        /// <summary>
        /// Company ID if this lookup group is specific to a company, null if global
        /// </summary>
        public Guid? CompanyId { get; set; }
    }

    /// <summary>
    /// Request model for updating an existing lookup group
    /// </summary>
    public class UpdateLookupGroupRequest : LookupGroupRequest
    {
        /// <summary>
        /// Unique identifier of the lookup group to update
        /// </summary>
        [Required]
        public Guid Id { get; set; }
    }
}
