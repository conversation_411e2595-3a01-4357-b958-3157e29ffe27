﻿using DocumentFormat.OpenXml.Spreadsheet;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Model;
using Dolfin.Utility.Enum;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Dolfin.Utility.Constant.Constant;
using static Dolfin.Utility.Enum.Enums;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using DocumentFormat.OpenXml.Vml.Office;
using Dolfin.Mobile.API.Models;

namespace Dolfin.Framework.Data.Entity
{
    public class DolfinDbContext : IdentityDbContext<ApplicationUser, ApplicationRole, string,
        IdentityUserClaim<string>, ApplicationUserRole, IdentityUserLogin<string>,
        IdentityRoleClaim<string>, IdentityUserToken<string>>
    {
        public DolfinDbContext(DbContextOptions<DolfinDbContext> options) : base(options)
        {
        }

        public DbSet<AccountGroup> AccountGroup { get; set; }
        public DbSet<AccountGroupByPeriod> AccountGroupByPeriod { get; set; }
        public DbSet<Address> Address { get; set; }
        public DbSet<Branch> Branch { get; set; }
        public DbSet<Company> Company { get; set; }
        public DbSet<Country> Country { get; set; }
        public DbSet<Currency> Currency { get; set; }
        public DbSet<Customer> Customer { get; set; }
        public DbSet<DebtorType> DebtorType { get; set; }
        public DbSet<EInvoice> EInvoice { get; set; }
        public DbSet<EInvoiceErrorLog> EInvoiceErrorLog { get; set; }
        public DbSet<EInvoiceSourceUpload> EInvoiceSourceUpload { get; set; }
        public DbSet<Email> Email { get; set; }
        public DbSet<FileUpload> FileUpload { get; set; }
        public DbSet<EmailAccount> EmailAccount { get; set; }
        public DbSet<EmailTemplate> EmailTemplate { get; set; }
        public DbSet<IdentityType> IdentityType { get; set; }
        public DbSet<Inventory> Inventory { get; set; }
        public DbSet<InventoryItem> InventoryItem { get; set; }
        public DbSet<InventoryItemTransactionItem> InventoryItemTransactionItem { get; set; }
        public DbSet<JobConfig> JobConfig { get; set; }
        public DbSet<PaymentType> PaymentType { get; set; }
        public DbSet<Product> Product { get; set; }
        public DbSet<ProductCategory> ProductCategory { get; set; }
        public DbSet<ProductCostMethod> ProductCostMethod { get; set; }
        public DbSet<ProductPrice> ProductPrice { get; set; }
        public DbSet<ProductUOM> ProductUOM { get; set; }
        public DbSet<Region> Region { get; set; }
        public DbSet<RegionState> RegionState { get; set; }
        public DbSet<Settings> Settings { get; set; }
        public DbSet<State> State { get; set; }
        public DbSet<Subscription> Subscription { get; set; }
        public DbSet<TaxCategories> TaxCategories { get; set; }
        public DbSet<TaxRate> TaxRate { get; set; }
        public DbSet<Term> Term { get; set; }
        public DbSet<TraceLogIntegration> TraceLogIntegration { get; set; }
        public DbSet<Transaction> Transaction { get; set; }
        public DbSet<TransactionItem> TransactionItem { get; set; }
        public DbSet<TransactionPaid> TransactionPaid { get; set; }
        public DbSet<TransactionStatus> TransactionStatus { get; set; }
        public DbSet<TransactionType> TransactionType { get; set; }
        public DbSet<UOM> UOM { get; set; }
        public DbSet<UOMCategories> UOMCategories { get; set; }
        public DbSet<Prefix> Prefix { get; set; }
        public DbSet<RefreshToken> RefreshTokens { get; set; }
        public DbSet<LookupGroup> LookupGroup { get; set; }
        public DbSet<LookupItem> LookupItem { get; set; }
        public DbSet<Msic> Msic { get; set; }
        public DbSet<MsicCategoryReference> MsicCategoryReference { get; set; }

        /*
         OnModelCreating mainly used to configured our EF model and insert master data if requireds
        */
        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            #region Entity Properties

            modelBuilder.Entity<ApplicationUser>(b =>
            {
                // Each User can have many UserClaims
                b.HasMany(e => e.Claims)
                    .WithOne()
                    .HasForeignKey(uc => uc.UserId)
                    .IsRequired();

                // Each User can have many UserLogins
                b.HasMany(e => e.Logins)
                    .WithOne()
                    .HasForeignKey(ul => ul.UserId)
                    .IsRequired();

                // Each User can have many UserTokens
                b.HasMany(e => e.Tokens)
                    .WithOne()
                    .HasForeignKey(ut => ut.UserId)
                    .IsRequired();

                // Each User can have many entries in the UserRole join table
                //b.HasMany(e => e.UserRoles)
                //    .WithOne(e => e.User)
                //    .HasForeignKey(ur => ur.UserId)
                //    .IsRequired();

                b.HasMany(e => e.Transaction)
                    .WithOne(e => e.User)
                    .HasForeignKey(ut => ut.UserId);

                b.HasOne(e => e.Company)
                    .WithMany(e => e.User)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CompanyId)
                    .HasConstraintName("FK_Company_User");

                b.HasOne(e => e.Branch)
                    .WithMany(e => e.User)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.BranchId)
                    .HasConstraintName("FK_Branch_User");
            });

            modelBuilder.Entity<ApplicationUserRole>()
                .HasKey(ur => new { ur.UserId, ur.RoleId });

            modelBuilder.Entity<ApplicationUserRole>()
                .HasOne(ur => ur.User)
                .WithMany(u => u.UserRoles)
                .HasForeignKey(ur => ur.UserId);

            modelBuilder.Entity<ApplicationUserRole>()
                .HasOne(ur => ur.Role)
                .WithMany(r => r.UserRoles)
                .HasForeignKey(ur => ur.RoleId);

            modelBuilder.Entity<AccountGroup>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<AccountGroupByPeriod>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.Year)
                    .IsRequired();

                entity.Property(e => e.Month)
                    .IsRequired();

                entity.Property(e => e.Locked)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.BranchId)
                    .IsRequired();

                entity.Property(e => e.TransactionTypeId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.Branch)
                    .WithMany(e => e.AccountGroupByPeriods)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.BranchId)
                    .HasConstraintName("FK_AccountGroupByPeriod_Branch");

                entity.HasOne(e => e.TransactionType)
                    .WithMany(e => e.AccountGroupByPeriods)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.TransactionTypeId)
                    .HasConstraintName("FK_AccountGroupByPeriod_TransactionType");
            });

            modelBuilder.Entity<Address>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.FirstName)
                    .IsRequired();

                entity.Property(e => e.LastName)
                    .IsRequired();

                entity.Property(e => e.Email)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Address1)
                    .IsRequired();

                entity.Property(e => e.PostalCode)
                    .IsRequired();

                entity.Property(e => e.PhoneNo)
                    .IsRequired()
                    .IsUnicode(); ;

                entity.Property(e => e.CountryId)
                    .IsRequired();

                entity.Property(e => e.StateProvinceId)
                    .IsRequired();

                entity.Property(e => e.RegionId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.Country)
                    .WithMany(e => e.Address)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CountryId)
                    .HasConstraintName("FK_Address_Country");

                entity.HasOne(e => e.State)
                    .WithMany(e => e.Address)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.StateProvinceId)
                    .HasConstraintName("FK_Address_State");

                entity.HasOne(e => e.Region)
                    .WithMany(e => e.Address)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.RegionId)
                    .HasConstraintName("FK_Address_Region");
            });

            modelBuilder.Entity<Branch>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.AddressId)
                    .IsRequired();

                entity.Property(e => e.CompanyId)
                    .IsRequired();

                entity.Property(e => e.IsHq)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.Address)
                    .WithMany(e => e.Branch)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.AddressId)
                    .HasConstraintName("FK_Branch_Address");

                entity.HasOne(e => e.Company)
                    .WithMany(e => e.Branch)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CompanyId)
                    .HasConstraintName("FK_Branch_Company");
            });

            modelBuilder.Entity<Company>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.RegNo)
                    .IsRequired();

                entity.Property(e => e.SstNo)
                    .IsRequired();

                entity.Property(e => e.DefaultSalesTaxNoId)
                    .IsRequired(false);

                entity.Property(e => e.DefaultServiceTaxNoId)
                    .IsRequired(false);

                entity.Property(e => e.TinNo)
                    .IsRequired();

                entity.Property(e => e.IsBranchSameProduct)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.ExpiredAt)
                    .IsRequired();

                entity.Property(e => e.CurrencyId)
                    .IsRequired();

                entity.Property(e => e.TaxCategoryId)
                    .IsRequired();

                entity.Property(e => e.IsEInvoiceSubmitAuto)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.ConsolidatePay)
                    .HasDefaultValueSql("5");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.DefaultSalesTaxNo)
                    .WithMany(e => e.CompanyDefaultSalesTaxNo)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.DefaultSalesTaxNoId)
                    .IsRequired(false)
                    .HasConstraintName("FK_Company_TaxRate_SalesTaxNo");

                entity.HasOne(e => e.DefaultServiceTaxNo)
                    .WithMany(e => e.CompanyDefaultServiceTaxNo)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.DefaultServiceTaxNoId)
                    .IsRequired(false)
                    .HasConstraintName("FK_Company_TaxRate_ServiceTaxNo");

                entity.HasOne(e => e.Currency)
                    .WithMany(e => e.Company)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CurrencyId)
                    .HasConstraintName("FK_Company_Currency");

                entity.HasOne(e => e.Subscription)
                    .WithMany(e => e.Company)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.SubscriptionId)
                    .HasConstraintName("FK_Company_Subscription");

                entity.HasOne(e => e.Msic)
                    .WithMany(e => e.Company)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.MsicId)
                    .HasConstraintName("FK_Company_Msic");

                entity.HasOne(e => e.TaxCategory)
                    .WithMany()
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.TaxCategoryId)
                    .HasConstraintName("FK_Company_TaxCategory");
            });


            modelBuilder.Entity<Country>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Published)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<Currency>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<Customer>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsTaxExempt)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsPICEditable)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.DefaultPIC)
                    .IsRequired();

                entity.Property(e => e.ReferralCode)
                    .IsRequired();

                //entity.Property(e => e.AccountCode)
                //    .IsRequired()
                //    .IsUnicode();

                entity.Property(e => e.DebtorTypeId)
                    .IsRequired();

                //entity.Property(e => e.ReferrerId)
                //    .IsRequired();

                entity.Property(e => e.AddressId)
                    .IsRequired(false);

                entity.Property(e => e.CurrencyId)
                    .IsRequired();

                //entity.Property(e => e.AccountGroupId)
                //    .IsRequired();

                entity.Property(e => e.TinVerifyStatus)
                    .HasConversion<int>()
                    .HasDefaultValue(TinVerifyStatusEnum.Unverified);

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.DebtorType)
                    .WithMany(e => e.Customer)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.DebtorTypeId)
                    .HasConstraintName("FK_Customer_DebtorType");

                entity.HasOne(e => e.Referrer)
                    .WithMany(e => e.CustomerReferral)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ReferrerId)
                    .HasConstraintName("FK_Customer_Referrer");

                entity.HasOne(e => e.Address)
                    .WithMany(e => e.Customer)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.AddressId)
                    .IsRequired(false)
                    .HasConstraintName("FK_Customer_Address");

                entity.HasOne(e => e.Currency)
                    .WithMany(e => e.Customer)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CurrencyId)
                    .HasConstraintName("FK_Customer_Currency");

                entity.HasOne(e => e.AccountGroup)
                    .WithMany(e => e.Customer)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.AccountGroupId)
                    .HasConstraintName("FK_Customer_AccountGroup");

                entity.HasOne(e => e.Company)
                    .WithMany(e => e.Cust)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CompanyId)
                    .HasConstraintName("FK_Customer_Company");

                entity.HasOne(e => e.IdentityType)
                    .WithMany()
                    .HasForeignKey(e => e.IdentityTypeId)
                    .IsRequired(false)
                    .HasConstraintName("FK_Customer_IdentityType");
            });


            modelBuilder.Entity<Email>(entity =>
            {
                entity.Property(e => e.AttachmentFileName).HasMaxLength(500);

                entity.Property(e => e.Bcc)
                    .HasMaxLength(500)
                    .IsUnicode(false)
                    .IsRequired(false);

                entity.Property(e => e.Body).IsRequired();

                entity.Property(e => e.Cc)
                    .HasMaxLength(500)
                    .IsRequired(false)
                    .IsUnicode(false);

                entity.Property(e => e.FromEmail)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.Property(e => e.FromName).HasMaxLength(100);

                entity.Property(e => e.IsActive)
                    .IsRequired()
                    .HasDefaultValueSql("true");

                entity.Property(e => e.ReplyToEmail)
                    .HasMaxLength(500)
                    .IsRequired(false)
                    .IsUnicode(false);

                entity.Property(e => e.ReplyToName)
                    .IsRequired(false)
                    .HasMaxLength(100);

                entity.Property(e => e.Subject)
                    .IsRequired();

                entity.Property(e => e.AttachmentFilePath)
                    .IsRequired(false);

                entity.Property(e => e.AttachmentFileName)
                    .IsRequired(false);

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.Property(e => e.ToEmail)
                    .IsRequired()
                    .HasMaxLength(500)
                    .IsUnicode(false);

                entity.HasOne(d => d.EmailAccount)
                    .WithMany(d => d.Emails)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.EmailAccountId)
                    .HasConstraintName("FK_Emails_EmailAccount");

                entity.Property(e => e.ToName).HasMaxLength(100);
            });

            modelBuilder.Entity<EmailAccount>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.DisplayName)
                    .HasMaxLength(500);

                entity.Property(e => e.Email)
                    .IsRequired()
                    .HasMaxLength(500);

                entity.Property(e => e.Host)
                    .IsRequired()
                    .HasMaxLength(500);

                entity.Property(e => e.Password)
                    .IsRequired()
                    .HasMaxLength(500);

                entity.Property(e => e.Protocol)
                    .HasMaxLength(10);

                entity.Property(e => e.Username)
                    .IsRequired()
                    .HasMaxLength(500);

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<EmailTemplate>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Subject)
                    .IsRequired();

                entity.Property(e => e.Body)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(d => d.EmailAccount)
                    .WithMany(d => d.EmailTemplate)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.EmailAccountId)
                    .HasConstraintName("FK_EmailTemplate_EmailAccount");
            });

            modelBuilder.Entity<DebtorType>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<EInvoice>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.InvoiceNo)
                    .IsRequired()
                    .IsUnicode()
                    .HasMaxLength(100);

                entity.Property(e => e.Status)
                    .HasConversion<int>()
                    .HasDefaultValue(EInvoiceStatusEnum.Pending);

                entity.HasOne(e => e.Customer)
                    .WithMany()
                    .HasForeignKey(e => e.CustomerId)
                    .HasConstraintName("FK_EInvoice_Customer");

                entity.HasOne(e => e.Transaction)
                    .WithMany()
                    .HasForeignKey(e => e.TransactionId)
                    .HasConstraintName("FK_EInvoice_Transaction");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<EInvoiceErrorLog>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.ErrorMessage)
                    .IsRequired();

                entity.HasOne(e => e.EInvoice)
                    .WithMany()
                    .HasForeignKey(e => e.EInvoiceId)
                    .HasConstraintName("FK_EInvoiceErrorLog_EInvoice");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<EInvoiceSourceUpload>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.FileUrl)
                    .IsRequired();

                entity.Property(e => e.SourceUploadStatus)
                    .HasConversion<int>()
                    .HasDefaultValue(SourceUploadStatusEnum.Pending);

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<FileUpload>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.FileUrl)
                    .IsRequired();

                entity.Property(e => e.CompanyId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.Company)
                    .WithMany()
                    .HasForeignKey(e => e.CompanyId)
                    .HasConstraintName("FK_FileUpload_Company");
            });

            modelBuilder.Entity<IdentityType>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired()
                    .IsUnicode();

                entity.HasMany(e => e.Customer)
                    .WithOne(e => e.IdentityType)
                    .HasForeignKey(e => e.IdentityTypeId)
                    .IsRequired(false)
                    .HasConstraintName("FK_Customer_IdentityType");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });


            modelBuilder.Entity<Inventory>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.Branch)
                    .WithMany(e => e.Inventory)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.BranchId)
                    .IsRequired()
                    .HasConstraintName("FK_Inventory_Branch");
            });

            modelBuilder.Entity<InventoryProduct>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.Product)
                    .WithMany(e => e.InventoryProduct)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ProductId)
                    .HasConstraintName("FK_InventoryProduct_Product");
            });

            modelBuilder.Entity<InventoryItem>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Cost)
                    .IsRequired();

                entity.Property(e => e.BalanceQuantity)
                    .IsRequired();

                entity.Property(e => e.StockQuantity)
                    .IsRequired();

                entity.Property(e => e.StockInAt)
                    .IsRequired();

                entity.Property(e => e.ProductUOMId)
                    .IsRequired();

                entity.Property(e => e.ProductId)
                    .IsRequired();

                entity.Property(e => e.InventoryProductId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.ProductUOM)
                    .WithMany(e => e.InventoryItem)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ProductUOMId)
                    .HasConstraintName("FK_InventoryItem_ProductUOM");

                entity.HasOne(e => e.Product)
                    .WithMany(e => e.InventoryItem)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ProductId)
                    .HasConstraintName("FK_InventoryItem_Product");

                entity.HasOne(e => e.InventoryProduct)
                    .WithMany(e => e.InventoryItem)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.InventoryProductId)
                    .HasConstraintName("FK_InventoryItem_InventoryProduct");
            });

            modelBuilder.Entity<InventoryItemTransactionItem>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Quantity)
                    .IsRequired();

                entity.Property(e => e.InventoryItemId)
                    .IsRequired();

                entity.Property(e => e.TransactionItemId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.InventoryItem)
                    .WithMany(e => e.InventoryItemTransactionItem)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.InventoryItemId)
                    .HasConstraintName("FK_InventoryItemTransactionItem_Product");

                entity.HasOne(e => e.TransactionItem)
                    .WithMany(e => e.InventoryItemTransactionItem)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.TransactionItemId)
                    .HasConstraintName("FK_InventoryItemTransactionItem_TransactionItem");
            });

            modelBuilder.Entity<JobConfig>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .HasMaxLength(50);

                entity.Property(e => e.LastProcessDate)
                    .HasDefaultValueSql("now()");
            });

            modelBuilder.Entity<PaymentType>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.AccountGroup)
                    .WithMany(e => e.PaymentType)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.AccountGroupId)
                    .HasConstraintName("FK_PaymentType_AccountGroup")
                    .IsRequired(false);
            });

            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsTaxExempt)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsTaxExcl)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.Sku)
                    .IsUnicode()
                    .IsRequired();

                entity.Property(e => e.AccountCode)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Published)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.CompanyId)
                    .IsRequired();

                entity.Property(e => e.ProductCategoryId)
                    .IsRequired();

                entity.Property(e => e.ProductCostMethodId)
                    .IsRequired();

                //entity.Property(e => e.ProductUOMId)
                //    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.Company)
                    .WithMany(e => e.Product)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CompanyId)
                    .HasConstraintName("FK_Product_Company");

                entity.HasOne(e => e.ProductCategory)
                    .WithMany(e => e.Product)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ProductCategoryId)
                    .HasConstraintName("FK_Product_ProductCategory");

                entity.HasOne(e => e.ProductCostMethod)
                    .WithMany(e => e.Product)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ProductCostMethodId)
                    .HasConstraintName("FK_Product_ProductCostMethod");

                entity.HasOne(e => e.CustomServiceTaxNo)
                    .WithMany(e => e.ProductCustomServiceTaxNo)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CustomServiceTaxNoId)
                    .IsRequired(false)
                    .HasConstraintName("FK_Product_CustomServiceTaxNo");

                entity.HasOne(e => e.CustomSalesTaxNo)
                    .WithMany(e => e.ProductCustomSalesTaxNo)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CustomSalesTaxNoId)
                    .IsRequired(false)
                    .HasConstraintName("FK_Product_CustomSalesTaxNo");

                entity.HasOne(e => e.Classification)
                    .WithMany(e => e.Product)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ClassificationId)
                    .HasConstraintName("FK_Product_Classification");

                entity.HasOne(e => e.Currency)
                    .WithMany(e => e.Product)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CurrencyId)
                    .HasConstraintName("FK_Product_Currency");

                //entity.HasOne(e => e.ProductUOM)
                //    .WithMany(e => e.Product)
                //    .HasPrincipalKey(e => e.Id)
                //    .HasForeignKey(e => e.ProductUOMId)
                //    .HasConstraintName("FK_Product_ProductUOM");

                entity.HasIndex(e => new { e.CompanyId, e.Code })
                    .IsUnique()
                    .HasDatabaseName("IX_Product_Company_Code_Unique");

                entity.HasIndex(e => new { e.CompanyId, e.Sku })
                    .IsUnique()
                    .HasDatabaseName("IX_Product_Company_Sku_Unique");
            });

            modelBuilder.Entity<ProductCategory>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.CompanyId)
                    .IsRequired();

                entity.HasOne(e => e.Company)
                    .WithMany(e => e.ProductCategory)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CompanyId)
                    .HasConstraintName("FK_ProductCategory_Company");

                entity.HasIndex(e => new { e.CompanyId, e.Code })
                    .IsUnique()
                    .HasDatabaseName("IX_ProductCategory_Company_Code_Unique");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<ProductCostMethod>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<ProductPrice>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Price)
                    .IsRequired();

                entity.Property(e => e.EffectiveAt)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.ProductUOMId)
                    .IsRequired();

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.ProductUOM)
                    .WithMany(e => e.ProductPrice)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ProductUOMId)
                    .HasConstraintName("FK_ProductPrice_ProductUOM");
            });

            modelBuilder.Entity<ProductUOM>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.FractionTotal)
                    .IsRequired();

                entity.Property(e => e.Fraction)
                    .IsRequired();

                entity.Property(e => e.Barcode)
                    .IsRequired();

                entity.Property(e => e.IsMainUom)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsPriceFollowUomMainId)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.PriceEditable)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.UomPrimaryId)
                    .IsRequired();

                entity.Property(e => e.ProductId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.UomPrimary)
                    .WithMany(e => e.ProductUOMPrimary)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.UomPrimaryId)
                    .HasConstraintName("FK_ProductUOM_UOM_Primary");

                entity.HasOne(e => e.UomSecondary)
                    .WithMany(e => e.ProductUOMSecondary)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.UomSecondaryId)
                    .HasConstraintName("FK_ProductUOM_UOM_Secondary");

                entity.HasOne(e => e.Product)
                    .WithMany(e => e.ProductUOM)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ProductId)
                    .HasConstraintName("FK_ProductUOM_Product");
            });

            modelBuilder.Entity<Region>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Published)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<RegionState>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.RegionId)
                    .IsRequired();

                entity.Property(e => e.StateId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.Region)
                    .WithMany(e => e.RegionState)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.RegionId)
                    .HasConstraintName("FK_RegionState_Region");

                entity.HasOne(e => e.State)
                    .WithMany(e => e.RegionState)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.StateId)
                    .HasConstraintName("FK_RegionState_State");
            });

            // Role entity configuration removed - now using ASP.NET Identity

            // RolePermission entity configuration removed - now using ASP.NET Identity

            modelBuilder.Entity<Settings>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.Type)
                    .IsRequired();

                entity.Property(e => e.Value)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<State>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Name)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Abbreviation)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Published)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.CountryId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.Country)
                    .WithMany(e => e.State)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CountryId)
                    .HasConstraintName("FK_State_Country");
            });

            modelBuilder.Entity<Subscription>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Description)
                    .IsRequired();

                entity.Property(e => e.Months)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Published)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<TaxCategories>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<TaxRate>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.ChargePercentage)
                    .IsRequired();

                entity.Property(e => e.TaxCategoryId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.TaxCategory)
                    .WithMany(e => e.TaxRate)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.TaxCategoryId)
                    .HasConstraintName("FK_TaxRate_TaxCategories");
            });

            modelBuilder.Entity<Term>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.Days)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<TraceLogIntegration>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Url)
                    .IsRequired();

                entity.Property(e => e.Type)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });


            modelBuilder.Entity<Transaction>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.TrxNo)
                    .IsRequired();

                entity.Property(e => e.TrxDatetime)
                    .IsRequired();

                entity.Property(e => e.CurrencyCode)
                    .IsRequired();

                entity.Property(e => e.ExchangeRate)
                    .IsRequired();

                entity.Property(e => e.TotalRoundingAdjustmentAmount)
                    .IsRequired();

                entity.Property(e => e.TotalAmount)
                    .IsRequired();

                entity.Property(e => e.TotalDiscount)
                    .IsRequired();

                entity.Property(e => e.TotalExclTaxAmount)
                    .IsRequired();

                entity.Property(e => e.TotalInclTaxAmount)
                    .IsRequired();

                entity.Property(e => e.TotalSalesTaxAmount)
                    .IsRequired();

                entity.Property(e => e.TotalServiceTaxAmount)
                    .IsRequired();

                entity.Property(e => e.SalesTaxNoId)
                    .IsRequired(false);

                entity.Property(e => e.ServiceTaxNoId)
                    .IsRequired(false);

                entity.Property(e => e.TotalPayableAmount)
                    .IsRequired();

                entity.Property(e => e.PIC)
                    .IsRequired();

                entity.Property(e => e.IsEInvoiceCreated)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.SalesTaxNo)
                    .WithMany(e => e.TransactionSalesTaxNo)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.SalesTaxNoId)
                    .IsRequired(false)
                    .HasConstraintName("FK_Transaction_TaxRate_SalesTaxNo");

                entity.HasOne(e => e.ServiceTaxNo)
                    .WithMany(e => e.TransactionServiceTaxNo)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ServiceTaxNoId)
                    .IsRequired(false)
                    .HasConstraintName("FK_Transaction_TaxRate_ServiceTaxNo");

                entity.HasOne(e => e.Term)
                    .WithMany(e => e.Transaction)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.TermId)
                    .HasConstraintName("FK_Transaction_Term");

                entity.HasOne(e => e.AccountGroup)
                    .WithMany(e => e.Transaction)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.AccountGroupId)
                    .HasConstraintName("FK_Transaction_AccountGroup");

                entity.HasOne(e => e.TransactionType)
                    .WithMany(e => e.Transaction)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.TransactionTypeId)
                    .HasConstraintName("FK_Transaction_TransactionType");

                entity.HasOne(e => e.Branch)
                    .WithMany(e => e.Transaction)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.BranchId)
                    .HasConstraintName("FK_Transaction_Branch");

                entity.HasOne(e => e.Customer)
                    .WithMany(e => e.Transaction)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CustomerId)
                    .HasConstraintName("FK_Transaction_Customer");

                //entity.HasOne(e => e.User)
                //    .WithMany(e => e.Transaction)
                //    .HasPrincipalKey(e => e.Id)
                //    .HasForeignKey(e => e.UserId)
                //    .HasConstraintName("FK_Transaction_User");

                entity.HasOne(e => e.ShippingAddress)
                    .WithMany(e => e.TransactionShippingAddress)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ShippingAddressId)
                    .HasConstraintName("FK_Transaction_Address_ShippingAddress");

                entity.HasOne(e => e.BillingAddress)
                    .WithMany(e => e.TransactionBillingAddress)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.BillingAddressId)
                    .HasConstraintName("FK_Transaction_Address_BillingAddress");

                entity.HasOne(e => e.TransactionStatus)
                    .WithMany(e => e.Transaction)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.TransactionStatusId)
                    .HasConstraintName("FK_Transaction_TransactionStatus");

                entity.HasIndex(e => new { e.TrxNo, e.BranchId })
                    .IsUnique()
                    .HasDatabaseName("IX_Transaction_TrxNo_BranchId_Unique");
            });

            modelBuilder.Entity<TransactionItem>(entity =>
            {
                entity.HasKey(e => e.Id);


                entity.Property(e => e.FractionTotal)
                    .IsRequired();

                entity.Property(e => e.FractionQuantity)
                    .IsRequired();

                entity.Property(e => e.Quantity)
                    .IsRequired();

                entity.Property(e => e.Discount)
                    .IsRequired();

                entity.Property(e => e.SalesTaxAmount)
                    .IsRequired();

                entity.Property(e => e.ServiceTaxAmount)
                    .IsRequired();

                entity.Property(e => e.SalesTaxNoId)
                    .IsRequired(false);

                entity.Property(e => e.ServiceTaxNoId)
                    .IsRequired(false);

                entity.Property(e => e.ExclTaxAmount)
                    .IsRequired();

                entity.Property(e => e.ExclTaxAmount)
                    .IsRequired();

                entity.Property(e => e.OriUnitProductCost)
                    .IsRequired();

                entity.Property(e => e.UnitAmount)
                    .IsRequired();

                entity.Property(e => e.TotalUnitAmount)
                    .IsRequired();

                entity.Property(e => e.ProductPriceId)
                    .IsRequired();

                entity.Property(e => e.TransactionItemStatusId)
                    .IsRequired(false);

                entity.Property(e => e.CustomerId)
                    .IsRequired();

                entity.Property(e => e.ProductUOMId)
                    .IsRequired();

                entity.Property(e => e.TrxId)
                    .IsRequired();

                entity.Property(e => e.ProductId)
                    .IsRequired();

                entity.Property(e => e.ClassificationId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.SalesTaxNo)
                    .WithMany(e => e.TransactionItemSalesTaxNo)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.SalesTaxNoId)
                    .HasConstraintName("FK_TransactionItem_TaxRate_SalesTaxNo");

                entity.HasOne(e => e.ServiceTaxNo)
                    .WithMany(e => e.TransactionItemServiceTaxNo)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ServiceTaxNoId)
                    .HasConstraintName("FK_TransactionItem_TaxRate_ServiceTaxNo");

                entity.HasOne(e => e.ProductPrice)
                    .WithMany(e => e.TransactionItem)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ProductPriceId)
                    .HasConstraintName("FK_TransactionItem_ProductPrice");

                entity.HasOne(e => e.ProductUOM)
                    .WithMany(e => e.TransactionItem)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ProductUOMId)
                    .HasConstraintName("FK_TransactionItem_ProductUOM");

                entity.HasOne(e => e.Transaction)
                    .WithMany(e => e.TransactionItem)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.TrxId)
                    .HasConstraintName("FK_TransactionItem_Transaction");

                entity.HasOne(e => e.Product)
                    .WithMany(e => e.TransactionItem)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ProductId)
                    .HasConstraintName("FK_TransactionItem_Product");

                entity.HasOne(e => e.Classification)
                    .WithMany(e => e.TransactionItem)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.ClassificationId)
                    .HasConstraintName("FK_TransactionItem_Classification");

                entity.HasOne(e => e.TransactionItemStatus)
                    .WithMany(e => e.TransactionItem)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.TransactionItemStatusId)
                    .HasConstraintName("FK_TransactionItem_TransactionItemStatus");
            });

            modelBuilder.Entity<TransactionPaid>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.PaidAmount)
                    .IsRequired();

                entity.Property(e => e.PaidAt)
                    .IsRequired();

                entity.Property(e => e.PaymentTypeId)
                    .IsRequired();

                entity.Property(e => e.TrxId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.PaymentType)
                    .WithMany(e => e.TransactionPaid)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.PaymentTypeId)
                    .HasConstraintName("FK_TransactionPaid_Product");

                entity.HasOne(e => e.Transaction)
                    .WithMany(e => e.TransactionPaid)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.TrxId)
                    .HasConstraintName("FK_TransactionPaid_Transaction");
            });

            modelBuilder.Entity<TransactionStatus>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<TransactionItemStatus>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<TransactionType>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<UOM>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.UOMCategory)
                    .WithMany(e => e.Uom)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.UOMCategoryId)
                    .HasConstraintName("FK_UOM_UOMCategory");
            });

            modelBuilder.Entity<UOMCategories>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<Prefix>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.TableName)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.PrefixValue)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.LastNumber)
                    .HasDefaultValue(0);

                entity.Property(e => e.PaddingLength)
                    .HasDefaultValue(5);

                entity.Property(e => e.BranchId)
                    .IsRequired();

                entity.Property(e => e.UserId)
                    .IsRequired(false);

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.Branch)
                    .WithMany()
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.BranchId)
                    .HasConstraintName("FK_Prefix_Branch");

                entity.HasOne(e => e.User)
                    .WithMany()
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.UserId)
                    .HasConstraintName("FK_Prefix_ApplicationUser")
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasIndex(e => new { e.TableName, e.BranchId, e.UserId })
                    .IsUnique()
                    .HasDatabaseName("IX_Prefix_TableName_BranchId_UserId_Unique");
            });

            modelBuilder.Entity<LookupGroup>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Name)
                    .IsRequired();

                entity.Property(e => e.IsSystem)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.Company)
                    .WithMany()
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.CompanyId)
                    .HasConstraintName("FK_LookupGroup_Company");
            });

            modelBuilder.Entity<LookupItem>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Value)
                    .IsRequired();

                entity.Property(e => e.IsSystem)
                    .HasDefaultValueSql("false");

                entity.Property(e => e.LookupGroupId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.LookupGroup)
                    .WithMany(e => e.LookupItems)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.LookupGroupId)
                    .HasConstraintName("FK_LookupItem_LookupGroup");
            });

            modelBuilder.Entity<MsicCategoryReference>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Description)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();
            });

            modelBuilder.Entity<Msic>(entity =>
            {
                entity.HasKey(e => e.Id);

                entity.Property(e => e.Code)
                    .IsRequired()
                    .IsUnicode();

                entity.Property(e => e.Description)
                    .IsRequired();

                entity.Property(e => e.MsicCategoryReferenceId)
                    .IsRequired();

                entity.Property(e => e.IsActive)
                    .HasDefaultValueSql("true");

                entity.Property(e => e.CreatedAt)
                    .HasDefaultValueSql("now()");

                entity.Property(e => e.CreatedBy)
                    .IsRequired();

                entity.HasOne(e => e.MsicCategoryReference)
                    .WithMany(e => e.Msic)
                    .HasPrincipalKey(e => e.Id)
                    .HasForeignKey(e => e.MsicCategoryReferenceId)
                    .HasConstraintName("FK_Msic_MsicCategoryReference");
            });

            // UserRole entity configuration removed - now using ASP.NET Identity
            #endregion

            #region Insert Master Data
            modelBuilder.Entity<Settings>().HasData(
               new Settings()
               {
                   Id = new Guid("929c9070-55d2-4b9d-832d-84f6e8bda3cd"),
                   Code = "SYSTEM_NAME",
                   Name = "System name",
                   Description = "Name of the system",
                   Type = "SYSTEM",
                   Value = "Dolfin Solutions",
                   CreatedBy = AdminConfiguration.SystemUser
               }
            );

            // Seed data for ASP.NET Identity roles
            modelBuilder.Entity<ApplicationRole>().HasData(
               new ApplicationRole()
               {
                   Id = "aef630d3-6f0b-4c1f-a17b-c60f8f0408ea", // Admin role ID
                   Name = "Administrator",
                   NormalizedName = "ADMINISTRATOR",
                   TargetType = "APP",
                   Editable = true,
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser
               },
               new ApplicationRole()
               {
                   Id = "b9c5d6a7-8e9f-4b0c-a1d2-e3f4g5h6i7j8", // SuperUser role ID
                   Name = "Super User",
                   NormalizedName = "SUPER USER",
                   TargetType = "ADMIN",
                   Editable = true,
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser
               },
               new ApplicationRole()
               {
                   Id = "c1d2e3f4-g5h6-i7j8-k9l0-m1n2o3p4q5r6", // SuperAdmin role ID
                   Name = "Super Admin",
                   NormalizedName = "SUPER ADMIN",
                   TargetType = "SYSADMIN",
                   Editable = true,
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser
               },
               new ApplicationRole()
               {
                   Id = "d4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9", // User role ID
                   Name = "User",
                   NormalizedName = "USER",
                   TargetType = "USER",
                   Editable = false,
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser
               }
            );

            modelBuilder.Entity<Currency>().HasData(
               new Currency()
               {
                   Id = new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                   Code = "MYR",
                   Name = "Malaysia Ringgit",
                   Symbol = "RM",
                   ExchangeRate = 1.0m,
                   Precision = 2,
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser
               }
            );

            var taxCategories = new List<TaxCategoryRequest> {
                new TaxCategoryRequest()
                {
                    TaxCategoryId = new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                    Code = "SALESTAX",
                    Name = "Sales Tax"
                },
                new TaxCategoryRequest()
                {
                    TaxCategoryId = new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                    Code = "SERVICETAX",
                    Name = "Service Tax"
                },
                new TaxCategoryRequest()
                {
                    TaxCategoryId = new Guid("82ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                    Code = "NOTAX",
                    Name = "Not Applicable"
                }
            };

            modelBuilder.Entity<TaxCategories>().HasData(
               new TaxCategories()
               {
                   Id = taxCategories.Find(x => x.Code == "SALESTAX").TaxCategoryId,
                   Code = taxCategories.Find(x => x.Code == "SALESTAX").Code,
                   Name = taxCategories.Find(x => x.Code == "SALESTAX").Name,
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser,
               },
               new TaxCategories()
               {
                   Id = taxCategories.Find(x => x.Code == "SERVICETAX").TaxCategoryId,
                   Code = taxCategories.Find(x => x.Code == "SERVICETAX").Code,
                   Name = taxCategories.Find(x => x.Code == "SERVICETAX").Name,
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser,
               },
               new TaxCategories()
               {
                   Id = taxCategories.Find(x => x.Code == "NOTAX").TaxCategoryId,
                   Code = taxCategories.Find(x => x.Code == "NOTAX").Code,
                   Name = taxCategories.Find(x => x.Code == "NOTAX").Name,
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser,
               }
            );

            //modelBuilder.Entity<TaxRate>().HasData(
            //   new TaxRate()
            //   {
            //       Id = new Guid("59bb4a30-7cae-47ab-800d-a117e0134ac5"),
            //       Code = "SR",
            //       Name = "Sales Rate",
            //       ChargePercentage = 0,
            //       TaxCategoryId = taxCategories.Find(x => x.Code == "SALESTAX").TaxCategoryId,
            //       IsActive = true,
            //       CreatedAt = DateTime.UtcNow,
            //       CreatedBy = AdminConfiguration.SystemUser
            //   }
            //);

            modelBuilder.Entity<ProductCostMethod>().HasData(
               new ProductCostMethod()
               {
                   Id = new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                   Code = "FIFO",
                   Name = "First In, First Out",
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser
               },
               new ProductCostMethod()
               {
                   Id = new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                   Code = "FILO",
                   Name = "First In, Last Out",
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser
               },
               new ProductCostMethod()
               {
                   Id = new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                   Code = "LIFO",
                   Name = "Last In, First Out",
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser
               },
               new ProductCostMethod()
               {
                   Id = new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                   Code = "WA",
                   Name = "Weighted Average",
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser
               }
            );

            // Seed data for ASP.NET Identity users
            // Note: In a real application, you should use a password hasher to create proper password hashes
            // This is just a placeholder - you should use UserManager to create users with proper password hashing
            var adminUserId = "7f3b2a5a-b8cd-4e08-a1b7-11b9e4b8c7a5";
            modelBuilder.Entity<ApplicationUser>().HasData(
               new ApplicationUser()
               {
                   Id = adminUserId,
                   UserName = "dolfinadmin",
                   NormalizedUserName = "DOLFINADMIN",
                   Email = "<EMAIL>",
                   NormalizedEmail = "<EMAIL>",
                   EmailConfirmed = true,
                   PhoneNo1 = "01116200503",
                   PhoneNumber = "01116200503",
                   PhoneNumberConfirmed = true,
                   SecurityStamp = Guid.NewGuid().ToString(),
                   PasswordExpireAt = DateTime.UtcNow.AddYears(10),
                   IsActive = true,
                   CreatedAt = DateTime.UtcNow,
                   CreatedBy = AdminConfiguration.SystemUser
                   // Note: Password hash should be created using a password hasher
                   // PasswordHash = passwordHasher.HashPassword(null, "Admin@123Doflin")
               }
            );

            // Assign admin user to admin role
            modelBuilder.Entity<ApplicationUserRole>().HasData(
               new ApplicationUserRole()
               {
                   UserId = adminUserId,
                   RoleId = "aef630d3-6f0b-4c1f-a17b-c60f8f0408ea" // Admin role ID
               }
            );
            #endregion
        }
    }
}
