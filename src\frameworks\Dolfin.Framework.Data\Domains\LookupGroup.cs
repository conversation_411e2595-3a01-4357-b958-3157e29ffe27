using System;
using System.Collections.Generic;

namespace Dolfin.Framework.Data.Domains
{
    /// <summary>
    /// Represents a group of lookup items for dropdown lists
    /// </summary>
    public partial class LookupGroup : _BaseDomain
    {
        public LookupGroup()
        {
            LookupItems = new HashSet<LookupItem>();
        }

        /// <summary>
        /// Unique code for the lookup group
        /// </summary>
        public required string Code { get; set; }

        /// <summary>
        /// Display name for the lookup group
        /// </summary>
        public required string Name { get; set; }

        /// <summary>
        /// Optional description for the lookup group
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Display order for the lookup group
        /// </summary>
        public int? DisplayOrder { get; set; }

        /// <summary>
        /// Indicates if the lookup group is system-defined (cannot be modified by users)
        /// </summary>
        public bool IsSystem { get; set; }

        /// <summary>
        /// Company ID if this lookup group is specific to a company, null if global
        /// </summary>
        public Guid? CompanyId { get; set; }

        /// <summary>
        /// Navigation property to the company
        /// </summary>
        public virtual Company? Company { get; set; }

        /// <summary>
        /// Collection of lookup items in this group
        /// </summary>
        public virtual ICollection<LookupItem> LookupItems { get; }
    }
}
