using AutoMapper;
using Dolfin.Mobile.API.Infrastructure;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Models.Response;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Framework.Repository.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Reflection;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Controllers
{
    /// <summary>
    /// Authentication controller for user registration, login, and account management
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerCore
    {
        private readonly ILogger<AuthController> _logger;
        private readonly IAuthService _authService;
        private readonly IJwtService _jwtService;
        private readonly IMapper _mapper;
        private readonly ICacheService _cacheService;

        /// <summary>
        /// Initializes a new instance of the <see cref="AuthController"/> class
        /// </summary>
        /// <param name="logger">The logger</param>
        /// <param name="authService">The authentication service</param>
        /// <param name="jwtService">The JWT service</param>
        /// <param name="mapper">The mapper</param>
        /// <param name="cacheService">The cache service</param>
        public AuthController(
            ILogger<AuthController> logger,
            IAuthService authService,
            IJwtService jwtService,
            IMapper mapper,
            ICacheService cacheService)
        {
            _logger = logger;
            _mapper = mapper;
            _authService = authService;
            _jwtService = jwtService;
            _cacheService = cacheService;
        }

        /// <summary>
        /// Registers a new user in the system
        /// </summary>
        /// <param name="model">The registration information</param>
        /// <returns>A confirmation message if successful, or error details if registration fails</returns>
        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] Register model)
        {
            var (response, newUserId) = await _authService.RegisterAsync(model);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultMessage, ResultMessage>(_mapper, response);
        }

        /// <summary>
        /// Authenticates a user and returns a JWT token
        /// </summary>
        /// <param name="model">The login credentials</param>
        /// <returns>JWT token and user information if login is successful</returns>
        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] Login model)
        {
            var response = await _authService.LoginAsync(model);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            else if (response.Result != null)
            {
                // Set authentication cookies
                SetAuthCookies(response.Result.Token, response.Result.RefreshToken);
                _logger.LogInformation("Authentication cookies set for user login");
            }
            return ActionResultResponse<AuthResultDto, AuthResultDto>(_mapper, response);
        }

        /// <summary>
        /// Logs out the current user
        /// </summary>
        /// <returns>A confirmation message</returns>
        [HttpPost("logout")]
        public async Task<IActionResult> Logout()
        {
            var userId = User?.Identity?.Name;
            var response = await _authService.LogoutAsync();

            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            else
            {
                // Clear authentication cookies to prevent data sync issues
                ClearAuthCookies();
                _logger.LogInformation("Authentication cookies cleared during logout for user {UserId}", userId);

                // Clear cache to prevent data leakage and ensure security
                await ClearUserCacheOnLogout(userId);
            }
            return ActionResultResponse<ResultMessage, ResultMessage>(_mapper, response);
        }

        /// <summary>
        /// Comprehensive cache clearing on logout with multiple fallback strategies
        /// </summary>
        /// <param name="userId">The user ID for targeted cache clearing</param>
        private async Task ClearUserCacheOnLogout(string userId)
        {
            var cacheOperationsSuccessful = 0;
            var totalCacheOperations = 0;

            try
            {
                _logger.LogInformation("Starting comprehensive cache clearing for logout. UserId: {UserId}", userId ?? "Unknown");

                // Strategy 1: Force company cache clear (most aggressive method for company data)
                totalCacheOperations++;
                try
                {
                    _cacheService.ForceCompanyCacheClear();
                    cacheOperationsSuccessful++;
                    _logger.LogInformation("Successfully executed ForceCompanyCacheClear() operation");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "ForceCompanyCacheClear() operation failed");
                }

                // Strategy 2: Clear all cache (secondary method)
                totalCacheOperations++;
                try
                {
                    _cacheService.ClearAll();
                    cacheOperationsSuccessful++;
                    _logger.LogInformation("Successfully executed ClearAll() cache operation");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "ClearAll() cache operation failed");
                }

                // Strategy 3: Clear user-specific cache (fallback)
                if (!string.IsNullOrEmpty(userId))
                {
                    totalCacheOperations++;
                    try
                    {
                        _cacheService.ClearUserCache(userId);
                        cacheOperationsSuccessful++;
                        _logger.LogInformation("Successfully executed ClearUserCache() for user {UserId}", userId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "ClearUserCache() operation failed for user {UserId}", userId);
                    }
                }

                // Strategy 4: Clear company-specific cache if we can determine the company
                totalCacheOperations++;
                try
                {
                    await ClearCompanyCacheForUser(userId);
                    cacheOperationsSuccessful++;
                    _logger.LogInformation("Successfully executed company cache clearing");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Company cache clearing failed");
                }

                // Strategy 5: Force garbage collection as final cleanup
                totalCacheOperations++;
                try
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                    cacheOperationsSuccessful++;
                    _logger.LogDebug("Successfully executed garbage collection");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Garbage collection failed");
                }

                // Log final results
                _logger.LogInformation("Cache clearing completed. Successful operations: {Successful}/{Total}",
                    cacheOperationsSuccessful, totalCacheOperations);

                if (cacheOperationsSuccessful == 0)
                {
                    _logger.LogError("ALL 5 cache clearing strategies failed during logout for user {UserId} - CRITICAL ISSUE", userId);
                }
                else if (cacheOperationsSuccessful < totalCacheOperations)
                {
                    _logger.LogWarning("Some cache clearing operations failed during logout for user {UserId}. " +
                        "Successful: {Successful}/{Total}", userId, cacheOperationsSuccessful, totalCacheOperations);
                }
                else
                {
                    _logger.LogInformation("All cache clearing strategies succeeded for user {UserId}", userId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error during cache clearing process for user {UserId}", userId);
            }
        }

        /// <summary>
        /// Attempts to clear company-specific cache for the user
        /// </summary>
        /// <param name="userId">The user ID</param>
        private async Task ClearCompanyCacheForUser(string userId)
        {
            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogDebug("Cannot clear company cache: userId is null or empty");
                return;
            }

            try
            {
                // Try to get the user's company information for targeted cache clearing
                // This is a best-effort approach
                var userClaims = User?.Claims;
                var companyIdClaim = userClaims?.FirstOrDefault(c => c.Type == "CompanyId")?.Value;

                if (!string.IsNullOrEmpty(companyIdClaim) && Guid.TryParse(companyIdClaim, out var companyId))
                {
                    _cacheService.ClearCompanyCache(companyId);
                    _logger.LogInformation("Cleared company cache for company {CompanyId}", companyId);
                }
                else
                {
                    _logger.LogDebug("Could not determine company ID from user claims for targeted cache clearing");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to clear company-specific cache for user {UserId}", userId);
                throw; // Re-throw to be caught by the calling method
            }
        }

        /// <summary>
        /// Confirms a user's email address
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <param name="code">The confirmation code sent to the user's email</param>
        /// <returns>A confirmation result</returns>
        [HttpGet("confirm-email")]
        public async Task<IActionResult> ConfirmEmail(string userId, string code)
        {
            var response = await _authService.ConfirmEmailAsync(userId, code);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultMessage, ResultMessage>(_mapper, response);
        }

        /// <summary>
        /// Resends the email confirmation link to the user
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>A confirmation message</returns>
        [HttpPost("resend-confirmation-email")]
        public async Task<IActionResult> ResendConfirmationEmail(string userId)
        {
            var response = await _authService.ResendConfirmationEmailAsync(userId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultMessage, ResultMessage>(_mapper, response);
        }

        /// <summary>
        /// Initiates the password reset process by sending a reset link to the user's email
        /// </summary>
        /// <param name="model">The forgot password request containing the user's email</param>
        /// <returns>A confirmation message if successful, or error details if the process fails</returns>
        [HttpPost("forgot-password")]
        public async Task<IActionResult> ForgotPassword([FromBody] ForgotPassword model)
        {
            var response = await _authService.ForgotPasswordAsync(model);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultMessage, ResultMessage>(_mapper, response);
        }

        /// <summary>
        /// Resets a user's password using the reset token
        /// </summary>
        /// <param name="model">The reset password request containing the new password, token, and email</param>
        /// <returns>A confirmation message if successful, or error details if the reset fails</returns>
        [HttpPost("reset-password")]
        public async Task<IActionResult> ResetPassword([FromBody] ResetPassword model)
        {
            var response = await _authService.ResetPasswordAsync(model);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultMessage, ResultMessage>(_mapper, response);
        }

        /// <summary>
        /// Changes the password for the authenticated user
        /// </summary>
        /// <param name="model">The change password request containing the current and new passwords</param>
        /// <returns>A confirmation message if successful, or error details if the change fails</returns>
        [HttpPost("change-password")]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePassword model)
        {
            var response = await _authService.ChangePasswordAsync(User.Identity.Name, model);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultMessage, ResultMessage>(_mapper, response);
        }
        /// <summary>
        /// Refreshes an access token using a refresh token
        /// </summary>
        /// <param name="model">The refresh token request</param>
        /// <returns>A new access token and refresh token if successful</returns>
        [HttpPost("refresh-token")]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest model)
        {
            try
            {
                _logger.LogInformation("Processing refresh token request");

                // Get the access token directly from HttpContext.Items
                var accessToken = HttpContext.Items["AccessToken"] as string;

                // Validate that we have both tokens
                if (string.IsNullOrEmpty(accessToken))
                {
                    _logger.LogWarning("Access token not found in Authorization header or cookies");
                    var errorResponse = new BaseResponse<AuthResultDto>
                    {
                        IsSuccessful = false,
                        StatusCode = (int)Enums.StatusCode.BadRequest,
                        StatusMessage = "Access token is required",
                        Result = new AuthResultDto { DisplayMessage = "Access token is required" }
                    };
                    return ActionResultResponse<AuthResultDto, AuthResultDto>(_mapper, errorResponse);
                }

                if (string.IsNullOrEmpty(model.Token))
                {
                    _logger.LogWarning("Refresh token not found in request body or cookies");
                    var errorResponse = new BaseResponse<AuthResultDto>
                    {
                        IsSuccessful = false,
                        StatusCode = (int)Enums.StatusCode.BadRequest,
                        StatusMessage = "Refresh token is required",
                        Result = new AuthResultDto { DisplayMessage = "Refresh token is required" }
                    };
                    return ActionResultResponse<AuthResultDto, AuthResultDto>(_mapper, errorResponse);
                }

                var response = await _authService.RefreshTokenAsync(model.Token, accessToken);

                if (!response.IsSuccessful)
                {
                    _logger.LogWarning("Token refresh failed: {Error}", response.Exception);
                }
                else
                {
                    _logger.LogInformation("Token refreshed successfully");

                    // Set the new tokens in cookies
                    if (response.Result != null)
                    {
                        SetAuthCookies(response.Result.Token, response.Result.RefreshToken);
                    }
                }

                return ActionResultResponse<AuthResultDto, AuthResultDto>(_mapper, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing refresh token request");

                var errorResponse = new BaseResponse<AuthResultDto>
                {
                    IsSuccessful = false,
                    StatusCode = (int)Enums.StatusCode.InternalServerError,
                    StatusMessage = "An error occurred while processing the refresh token",
                    Exception = ex.ToString()
                };

                return ActionResultResponse<AuthResultDto, AuthResultDto>(_mapper, errorResponse);
            }
        }

        /// <summary>
        /// Revokes a refresh token, preventing it from being used to obtain new access tokens
        /// </summary>
        /// <param name="model">The token revocation request</param>
        /// <returns>A confirmation message if successful, or error details if the revocation fails</returns>
        [HttpPost("revoke-token")]
        public async Task<IActionResult> RevokeToken([FromBody] RevokeTokenRequest model)
        {
            try
            {
                _logger.LogInformation("Processing token revocation request");

                if (string.IsNullOrEmpty(model.Token))
                {
                    _logger.LogWarning("Refresh token not found in request body");
                    var errorResponse = new BaseResponse<ResultMessage>
                    {
                        IsSuccessful = false,
                        StatusCode = (int)Enums.StatusCode.BadRequest,
                        StatusMessage = "Refresh token is required",
                        Result = new ResultMessage { DisplayMessage = "Refresh token is required" }
                    };
                    return ActionResultResponse<ResultMessage, ResultMessage>(_mapper, errorResponse);
                }

                // Use the AuthService to revoke the token
                var response = await _authService.RevokeTokenAsync(model.Token, model.Reason);

                // If successful, clear the refresh token cookie
                if (response.IsSuccessful && Request.Cookies.ContainsKey("X-Refresh-Token"))
                {
                    Response.Cookies.Delete("X-Refresh-Token");
                    _logger.LogInformation("Refresh token cookie cleared");
                }

                return ActionResultResponse<ResultMessage, ResultMessage>(_mapper, response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing token revocation request");

                var errorResponse = new BaseResponse<ResultMessage>
                {
                    IsSuccessful = false,
                    StatusCode = (int)Enums.StatusCode.InternalServerError,
                    StatusMessage = "An error occurred while processing the token revocation",
                    Exception = ex.ToString(),
                    Result = new ResultMessage { DisplayMessage = "An error occurred while processing the token revocation" }
                };

                return ActionResultResponse<ResultMessage, ResultMessage>(_mapper, errorResponse);
            }
        }

        /// <summary>
        /// Sets authentication cookies for access token and refresh token
        /// </summary>
        /// <param name="accessToken">The JWT access token</param>
        /// <param name="refreshToken">The refresh token</param>
        private void SetAuthCookies(string accessToken, string refreshToken)
        {
            _logger.LogInformation("Setting authentication cookies");

            // Get cookie options from JwtService
            var (accessTokenOptions, refreshTokenOptions) = _jwtService.GetTokenCookieOptions(
                Request.IsHttps,
                accessToken,
                refreshToken);

            // Set the cookies
            Response.Cookies.Append("X-Access-Token", accessToken, accessTokenOptions);
            Response.Cookies.Append("X-Refresh-Token", refreshToken, refreshTokenOptions);

            // Set security headers
            var headers = Response.Headers;
            headers.XContentTypeOptions = "nosniff";
            headers.XFrameOptions = "DENY";
            headers.XXSSProtection = "1; mode=block";
        }

        /// <summary>
        /// Test endpoint to manually clear cache - FOR DEBUGGING ONLY
        /// </summary>
        [HttpPost("debug/clear-cache")]
        public IActionResult DebugClearCache()
        {
            try
            {
                _logger.LogWarning("DEBUG: Manual cache clearing requested");

                // Try all cache clearing methods
                var results = new List<string>();

                try
                {
                    _cacheService.ForceCompanyCacheClear();
                    results.Add("ForceCompanyCacheClear: SUCCESS");
                }
                catch (Exception ex)
                {
                    results.Add($"ForceCompanyCacheClear: FAILED - {ex.Message}");
                }

                try
                {
                    _cacheService.ClearAll();
                    results.Add("ClearAll: SUCCESS");
                }
                catch (Exception ex)
                {
                    results.Add($"ClearAll: FAILED - {ex.Message}");
                }

                try
                {
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                    results.Add("GarbageCollection: SUCCESS");
                }
                catch (Exception ex)
                {
                    results.Add($"GarbageCollection: FAILED - {ex.Message}");
                }

                _logger.LogWarning("DEBUG: Cache clearing results: {Results}", string.Join(", ", results));

                return Ok(new {
                    message = "Cache clearing attempted",
                    results = results,
                    timestamp = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "DEBUG: Cache clearing failed");
                return BadRequest(new { error = ex.Message });
            }
        }

        /// <summary>
        /// Clears authentication cookies to prevent data synchronization issues
        /// </summary>
        private void ClearAuthCookies()
        {
            _logger.LogInformation("Clearing authentication cookies");

            // Clear the authentication cookies by setting them to expire immediately
            Response.Cookies.Delete("X-Access-Token");
            Response.Cookies.Delete("X-Refresh-Token");

            // Also clear any ASP.NET Core Identity authentication cookie if it exists
            if (Request.Cookies.ContainsKey("Dolfin.Auth"))
            {
                Response.Cookies.Delete("Dolfin.Auth");
                _logger.LogInformation("ASP.NET Core Identity authentication cookie cleared");
            }
        }
    }
}
