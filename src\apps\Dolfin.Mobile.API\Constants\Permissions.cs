﻿﻿namespace Dolfin.Mobile.API.Constants
{
    /// <summary>
    /// Defines all permissions used in the application
    /// </summary>
    public static class Permissions
    {
        /// <summary>
        /// Lookup-related permissions
        /// </summary>
        public static class Lookup
        {
            // Group permissions
            public const string ViewGroups = "Permissions.Lookup.ViewGroups";
            public const string ViewGroup = "Permissions.Lookup.ViewGroup";
            public const string CreateGroup = "Permissions.Lookup.CreateGroup";
            public const string UpdateGroup = "Permissions.Lookup.UpdateGroup";
            public const string DeleteGroup = "Permissions.Lookup.DeleteGroup";

            // Item permissions
            public const string ViewItems = "Permissions.Lookup.ViewItems";
            public const string ViewItem = "Permissions.Lookup.ViewItem";
            public const string CreateItem = "Permissions.Lookup.CreateItem";
            public const string UpdateItem = "Permissions.Lookup.UpdateItem";
            public const string DeleteItem = "Permissions.Lookup.DeleteItem";
        }

        /// <summary>
        /// User-related permissions
        /// </summary>
        public static class User
        {
            public const string View = "Permissions.User.View";
            public const string Create = "Permissions.User.Create";
            public const string Update = "Permissions.User.Update";
            public const string Delete = "Permissions.User.Delete";
            public const string ManageRoles = "Permissions.User.ManageRoles";
        }

        /// <summary>
        /// Company-related permissions
        /// </summary>
        public static class Company
        {
            public const string View = "Permissions.Company.View";
            public const string Create = "Permissions.Company.Create";
            public const string Update = "Permissions.Company.Update";
            public const string Delete = "Permissions.Company.Delete";
            public const string ManageBranches = "Permissions.Company.ManageBranches";
        }

        /// <summary>
        /// Product-related permissions
        /// </summary>
        public static class Product
        {
            public const string View = "Permissions.Product.View";
            public const string Create = "Permissions.Product.Create";
            public const string Update = "Permissions.Product.Update";
            public const string Delete = "Permissions.Product.Delete";
            public const string ManageCategories = "Permissions.Product.ManageCategories";
        }

        /// <summary>
        /// Customer-related permissions
        /// </summary>
        public static class Customer
        {
            public const string View = "Permissions.Customer.View";
            public const string Create = "Permissions.Customer.Create";
            public const string Update = "Permissions.Customer.Update";
            public const string Delete = "Permissions.Customer.Delete";
        }

        /// <summary>
        /// Transaction-related permissions
        /// </summary>
        public static class Transaction
        {
            public const string View = "Permissions.Transaction.View";
            public const string Create = "Permissions.Transaction.Create";
            public const string Update = "Permissions.Transaction.Update";
            public const string Delete = "Permissions.Transaction.Delete";
            public const string ManageStatuses = "Permissions.Transaction.ManageStatuses";
        }

        /// <summary>
        /// File upload permissions
        /// </summary>
        public static class FileUpload
        {
            public const string Upload = "Permissions.FileUpload.Upload";
            public const string Delete = "Permissions.FileUpload.Delete";
        }

        /// <summary>
        /// Admin-related permissions
        /// </summary>
        public static class Admin
        {
            public const string ManageSettings = "Permissions.Admin.ManageSettings";
            public const string ViewLogs = "Permissions.Admin.ViewLogs";
            public const string ManageRoles = "Permissions.Admin.ManageRoles";
            public const string ManagePermissions = "Permissions.Admin.ManagePermissions";
        }

        /// <summary>
        /// EInvoice-related permissions
        /// </summary>
        public static class EInvoice
        {
            public const string View = "Permissions.EInvoice.View";
            public const string Create = "Permissions.EInvoice.Create";
            public const string Update = "Permissions.EInvoice.Update";
            public const string Cancel = "Permissions.EInvoice.Cancel";
            public const string Reject = "Permissions.EInvoice.Reject";
        }
    }
}
