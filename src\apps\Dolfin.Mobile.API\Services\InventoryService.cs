﻿using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.EntityFrameworkCore;
using static Dolfin.Utility.Enum.Enums;
using System.Linq.Expressions;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Microsoft.EntityFrameworkCore.Storage;

namespace Dolfin.Mobile.API.Services
{
    public class InventoryService : BaseComponent<Inventory>, IInventoryService
    {
        private readonly StandardMessage _standardMessage;
        private readonly IUserService _userService;
        private readonly IMapper _mapper;


        public InventoryService(DbContextOptions<DolfinDbContext> dbContextOptions, ISettingsService settingService, IUserService userService, IMapper mapper) : base(dbContextOptions)
        {
            _standardMessage = new StandardMessage();
            _userService = userService;
            _mapper = mapper;
        }

        #region  Inventory
        public async Task<BaseResponse<PagedList<Inventory>>> GetInventoryList(Pagination pagination = null, CommonFilterList filterList = null, Guid? branchId = null)
        {
            var result = new BaseResponse<PagedList<Inventory>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithBranchAsync(branchId);
                var currentUser = getCurrentUser.Item1;
                branchId = getCurrentUser.Item2;
                var companyId = currentUser.CompanyId;

                Expression<Func<Inventory, bool>> predicate = x => (x.Branch.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.BranchId == branchId || SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))))
                        && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var dbContext = GetDbContext();
                var query = dbContext.Set<Inventory>()
                    .Include(u => u.Branch).ThenInclude(u => u.Company)
                    .Where(predicate)
                    .Where(u => u.Branch.IsActive)
                    .Where(u => u.Branch.Company.IsActive)
                    .AsQueryable();

                var response = await query.ToListAsync();
                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<Inventory>, PagedList<Inventory>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<List<Inventory>>> GetInventoryListByCompanyBranch(Guid companyId, Guid? branchId)
        {
            var result = new BaseResponse<List<Inventory>> { IsSuccessful = true };
            try
            {
                Expression<Func<Inventory, bool>> predicate = x => (x.Branch.CompanyId == companyId) && (x.BranchId == branchId || (branchId == null))
                        && x.IsActive;

                var query = GetDbContext().Set<Inventory>()
                    .Include(u => u.Branch).ThenInclude(u => u.Company)
                    .Where(predicate)
                    .Where(u => u.Branch.IsActive)
                    .Where(u => u.Branch.Company.IsActive)
                    .AsQueryable();

                var response = await query.ToListAsync();
                result.Result = response;
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<List<Inventory>, List<Inventory>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<Inventory>> GetInventoryByGuid(Guid inventoryId)
        {
            var result = new BaseResponse<Inventory> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;
                var branchId = currentUser.BranchId;

                var query = GetDbContext().Set<Inventory>()
                    .Include(u => u.Branch).ThenInclude(u => u.Company)
                    .Where(x => x.Id == inventoryId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(x => (x.Branch.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.BranchId == branchId || SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(u => u.Branch.IsActive)
                    .Where(u => u.Branch.Company.IsActive)
                    .AsQueryable();
                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Inventory, Inventory>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertInventory(InventoryRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithBranchAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    var newInventory = _mapper.Map<Inventory>(reqBody);
                    newInventory.IsActive = true;
                    newInventory.CreatedAt = DateTime.UtcNow;
                    newInventory.CreatedBy = Guid.Parse(currentUser.Id);
                    var inventory = await CreateAsync(newInventory, dbContext);

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                    result.Result = new ResultId { Id = inventory.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateInventory(UpdateInventoryRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;
                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithBranchAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    var inventory = await GetInventoryByGuid(reqBody.Id);
                    if (!inventory.IsSuccessful)
                        throw new Exception(inventory.Exception);
                    else if (inventory == null || inventory.Result?.Id == null)
                        throw new Exception($"{reqBody.Id} not found.");
                    else
                    {
                        var existingInventory = inventory.Result;

                        // Update properties directly
                        if (reqBody.Name != null)
                            existingInventory.Name = reqBody.Name;

                        // BranchId should not be updated as it's a key relationship

                        // Update audit fields
                        existingInventory.UpdatedAt = DateTime.UtcNow;
                        existingInventory.UpdatedBy = Guid.Parse(currentUser.Id);

                        await UpdateAsync(existingInventory, dbContext);

                        if (currentTransaction == null)
                            await transaction.CommitAsync();
                        result.Result = new ResultId { Id = existingInventory.Id };
                    }
                }
                catch (Exception ex)
                {
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<NoResultResponse> DeleteInventory(Guid id)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            try
            {
                var inventory = await GetInventoryByGuid(id);
                if (!inventory.IsSuccessful)
                    throw new Exception(inventory.Exception);
                if (inventory == null || inventory.Result?.Id == null)
                    throw new Exception($"{id} not found.");

                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())) && currentUser.CompanyId != inventory.Result.Branch.CompanyId)
                    throw new Exception($"{id} not found.");

                var deleteInventory = inventory.Result;
                deleteInventory.IsActive = false;
                deleteInventory.UpdatedAt = DateTime.UtcNow;
                deleteInventory.UpdatedBy = Guid.Parse(currentUser.Id);
                await UpdateAsync(deleteInventory);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
        #endregion

        #region  Inventory Product
        //public async Task<BaseResponse<PagedList<InventoryProduct>>> GetInventoryProductList(Pagination pagination = null, CommonFilterList filterList = null, Guid? branchId = null)
        //{
        //    var result = new BaseResponse<PagedList<InventoryProduct>> { IsSuccessful = true };
        //    try
        //    {
        //        var getCurrentUser = await _userService.GetCurrentUserStdGetWithBranchAsync(branchId);
        //        var currentUser = getCurrentUser.Item1;
        //        branchId = getCurrentUser.Item2;
        //        var companyId = currentUser.CompanyId;

        //        Expression<Func<InventoryProduct, bool>> predicate = x => x.Inventory.Branch.CompanyId == companyId && (x.Inventory.BranchId == branchId || (branchId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
        //                && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))));

        //        if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
        //            predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

        //        var query = GetDbContext().Set<InventoryProduct>()
        //            .Include(u => u.Inventory).ThenInclude(u => u.Branch).ThenInclude(u => u.Company)
        //            .Where(predicate)
        //            .AsQueryable();

        //        var response = await query.ToListAsync();
        //        result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
        //    }
        //    catch (Exception ex)
        //    {
        //        result = _standardMessage.ErrorMessage<PagedList<InventoryProduct>, PagedList<InventoryProduct>>(result, Enums.StatusCode.InternalServerError, exception: ex);
        //    }
        //    return result;
        //}

        public async Task<BaseResponse<List<InventoryProduct>>> GetInventoryProductListByCompanyBranch(Guid companyId, Guid? branchId)
        {
            var result = new BaseResponse<List<InventoryProduct>> { IsSuccessful = true };
            try
            {
                Expression<Func<InventoryProduct, bool>> predicate = x => (x.Inventory.Branch.CompanyId == companyId) && (x.Inventory.BranchId == branchId || (branchId == null))
                        && x.IsActive;

                var query = GetDbContext().Set<InventoryProduct>()
                    .Include(u => u.Inventory).ThenInclude(u => u.Branch).ThenInclude(u => u.Company)
                    .Where(predicate)
                    .Where(u => u.Inventory.IsActive)
                    .Where(u => u.Inventory.Branch.IsActive)
                    .Where(u => u.Inventory.Branch.Company.IsActive)
                    .AsQueryable();

                var response = await query.ToListAsync();
                result.Result = response;
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<List<InventoryProduct>, List<InventoryProduct>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<InventoryProduct>> GetInventoryProductByGuid(Guid inventoryId)
        {
            var result = new BaseResponse<InventoryProduct> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;
                var branchId = currentUser.BranchId;

                var query = GetDbContext().Set<InventoryProduct>()
                    .Include(u => u.Inventory).ThenInclude(u => u.Branch).ThenInclude(u => u.Company)
                    .Where(x => x.Id == inventoryId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(x => (x.Inventory.Branch.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.Inventory.BranchId == branchId || SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(u => u.Inventory.IsActive)
                    .Where(u => u.Inventory.Branch.IsActive)
                    .Where(u => u.Inventory.Branch.Company.IsActive)
                    .AsQueryable();
                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<InventoryProduct, InventoryProduct>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertInventoryProduct(InventoryProductRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithBranchAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    var newInventoryProduct = _mapper.Map<InventoryProduct>(reqBody);
                    newInventoryProduct.BalanceQuantity = 0;
                    newInventoryProduct.SafeQuantity = 0;
                    newInventoryProduct.IsActive = true;
                    newInventoryProduct.CreatedAt = DateTime.UtcNow;
                    newInventoryProduct.CreatedBy = Guid.Parse(currentUser.Id);
                    var inventoryProduct = await CreateAsync(newInventoryProduct, dbContext);

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                    result.Result = new ResultId { Id = inventoryProduct.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        private async Task<BaseResponse<ResultId>> UpdateInventoryProduct(UpdateInventoryProductRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;
                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithBranchAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    var inventoryProduct = await GetInventoryProductByGuid(reqBody.Id);
                    if (!inventoryProduct.IsSuccessful)
                        throw new Exception(inventoryProduct.Exception);
                    else if (inventoryProduct == null || inventoryProduct.Result?.Id == null)
                        throw new Exception($"{reqBody.Id} not found.");
                    else
                    {
                        var existingInventoryProduct = inventoryProduct.Result;

                        // Update quantities based on flags
                        if (reqBody.IsAddSafeQuantity)
                            existingInventoryProduct.SafeQuantity = existingInventoryProduct.SafeQuantity + reqBody.UnitQuantity;
                        else if (reqBody.IsRemoveSafeQuantity)
                            existingInventoryProduct.SafeQuantity = existingInventoryProduct.SafeQuantity - reqBody.UnitQuantity;
                        else
                            existingInventoryProduct.BalanceQuantity = existingInventoryProduct.BalanceQuantity + reqBody.UnitQuantity;

                        // Update audit fields
                        existingInventoryProduct.UpdatedAt = DateTime.UtcNow;
                        existingInventoryProduct.UpdatedBy = Guid.Parse(currentUser.Id);

                        await UpdateAsync(existingInventoryProduct, dbContext);

                        if (currentTransaction == null)
                            await transaction.CommitAsync();
                        result.Result = new ResultId { Id = existingInventoryProduct.Id };
                    }
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }
        #endregion

        #region Inventory Item
        public async Task<BaseResponse<InventoryItem>> GetInventoryItemByGuid(Guid inventoryItemId)
        {
            var result = new BaseResponse<InventoryItem> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = getCurrentUser.Item2;
                var branchId = currentUser.BranchId;

                var query = GetDbContext().Set<InventoryItem>()
                    .Include(u => u.InventoryProduct).ThenInclude(u => u.Inventory).ThenInclude(u => u.Branch).ThenInclude(u => u.Company)
                    .Where(x => x.Id == inventoryItemId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(x => (x.InventoryProduct.Inventory.Branch.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.InventoryProduct.Inventory.BranchId == branchId || SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(u => u.InventoryProduct.IsActive)
                    .Where(u => u.InventoryProduct.Inventory.IsActive)
                    .Where(u => u.InventoryProduct.Inventory.Branch.IsActive)
                    .Where(u => u.InventoryProduct.Inventory.Branch.Company.IsActive)
                    .AsQueryable();
                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<InventoryItem, InventoryItem>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertInventoryItem(InventoryItemRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithBranchAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    // Update header table balance quantity
                    var updateInventoryProductRequest = new UpdateInventoryProductRequest()
                    {
                        Id = reqBody.InventoryProductId,
                        UnitQuantity = await InventoryProductUnitQuantityCalculation(reqBody.ProductUOMId, reqBody.StockQuantity, currentUser)
                    };
                    await UpdateInventoryProduct(updateInventoryProductRequest, dbContext);

                    var newInventoryProduct = _mapper.Map<InventoryProduct>(reqBody);
                    newInventoryProduct.BalanceQuantity = reqBody.StockQuantity;
                    newInventoryProduct.SafeQuantity = 0;
                    newInventoryProduct.IsActive = true;
                    newInventoryProduct.CreatedAt = DateTime.UtcNow;
                    newInventoryProduct.CreatedBy = Guid.Parse(currentUser.Id);
                    var inventoryProduct = await CreateAsync(newInventoryProduct, dbContext);

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                    result.Result = new ResultId { Id = inventoryProduct.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        /// <summary>
        /// Only use during create sales order
        /// </summary>
        /// <param name="reqBody"></param>
        /// <param name="dbContextRollback"></param>
        /// <returns></returns>
        public async Task<BaseResponse<ResultId>> UpdateInventoryItem(UpdateInventoryItemRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithBranchAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    // Get the inventory item to update
                    var inventoryItem = await GetInventoryItemByGuid(reqBody.Id);
                    if (!inventoryItem.IsSuccessful)
                        throw new Exception(inventoryItem.Exception);
                    else if (inventoryItem == null || inventoryItem.Result?.Id == null)
                        throw new Exception($"{reqBody.Id} not found.");

                    var existingInventoryItem = inventoryItem.Result;

                    // Update stock quantity
                    existingInventoryItem.StockQuantity = reqBody.StockQuantity;

                    // Update audit fields
                    existingInventoryItem.UpdatedAt = DateTime.UtcNow;
                    existingInventoryItem.UpdatedBy = Guid.Parse(currentUser.Id);

                    // Update the inventory item
                    await UpdateAsync(existingInventoryItem, dbContext);

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                    result.Result = new ResultId { Id = existingInventoryItem.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<int> InventoryProductUnitQuantityCalculation(Guid productUOMId, decimal stockQuantity, ApplicationUser currentUser)
        {
            // First, get the ProductUOM with its basic relationships
            var productUOM = await GetDbContext().Set<ProductUOM>()
                .Include(u => u.UomPrimary)
                .Include(u => u.UomSecondary)
                .Include(u => u.Product).ThenInclude(u => u.Company)
                .Include(u => u.ProductPrice.Where(x => x.IsActive)) // Include all prices
                .Where(x => x.Id == productUOMId &&
                       (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                .Where(x => x.Product.CompanyId == currentUser.CompanyId ||
                       (currentUser.CompanyId == null && SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                .Where(u => u.UomPrimary.IsActive)
                .Where(u => u.UomSecondary == null || u.UomSecondary.IsActive)
                .Where(u => u.Product.IsActive)
                .Where(u => u.Product.Company.IsActive)
                .FirstOrDefaultAsync();

            if (productUOM == null)
                throw new Exception($"ProductUOM with ID {productUOMId} not found.");

            // Set the EffectivedProductPrice to the most recent active price if needed
            if (productUOM.EffectivedProductPrice == null && productUOM.ProductPrice != null && productUOM.ProductPrice.Any())
            {
                productUOM.EffectivedProductPrice = productUOM.ProductPrice
                    .Where(p => p.IsActive && p.EffectiveAt <= DateTime.UtcNow)
                    .OrderByDescending(p => p.EffectiveAt)
                    .FirstOrDefault();
            }

            return SharedFunctionHelper.CalculationUnitQuantity(productUOM.FractionTotal * productUOM.EffectivedProductPrice.FractionQty, stockQuantity);
        }
        #endregion
    }
}