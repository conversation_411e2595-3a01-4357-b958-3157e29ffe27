﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Dolfin.Framework.Data.Migrations
{
    /// <inheritdoc />
    public partial class taxcategoryandmsic : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "aef630d3-6f0b-4c1f-a17b-c60f8f0408ea",
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2285));

            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "b9c5d6a7-8e9f-4b0c-a1d2-e3f4g5h6i7j8",
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2296));

            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "c1d2e3f4-g5h6-i7j8-k9l0-m1n2o3p4q5r6",
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2306));

            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "d4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9",
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2316));

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "7f3b2a5a-b8cd-4e08-a1b7-11b9e4b8c7a5",
                columns: new[] { "ConcurrencyStamp", "CreatedAt", "PasswordExpireAt", "SecurityStamp" },
                values: new object[] { "bc529bfc-b0cc-4925-8473-73b1a212360b", new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2773), new DateTime(2035, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2735), "b6f04c9e-be07-4bd8-9c2a-cbd2929de075" });

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2414));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2620));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2610));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2605));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2615));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2510));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2523));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("82ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 52, 27, 669, DateTimeKind.Utc).AddTicks(2534));
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "aef630d3-6f0b-4c1f-a17b-c60f8f0408ea",
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6121));

            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "b9c5d6a7-8e9f-4b0c-a1d2-e3f4g5h6i7j8",
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6125));

            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "c1d2e3f4-g5h6-i7j8-k9l0-m1n2o3p4q5r6",
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6143));

            migrationBuilder.UpdateData(
                table: "AspNetRoles",
                keyColumn: "Id",
                keyValue: "d4e5f6g7-h8i9-j0k1-l2m3-n4o5p6q7r8s9",
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6146));

            migrationBuilder.UpdateData(
                table: "AspNetUsers",
                keyColumn: "Id",
                keyValue: "7f3b2a5a-b8cd-4e08-a1b7-11b9e4b8c7a5",
                columns: new[] { "ConcurrencyStamp", "CreatedAt", "PasswordExpireAt", "SecurityStamp" },
                values: new object[] { "7dd3dc32-0b18-40c2-8d12-a3f0d5b3e27a", new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6392), new DateTime(2035, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6364), "2e257e09-50bb-4553-9251-1b64f18c653d" });

            migrationBuilder.UpdateData(
                table: "Currency",
                keyColumn: "Id",
                keyValue: new Guid("69ed35dc-8775-4641-9f1d-26d9d91e5f4b"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6199));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("11fd5e73-51fa-428a-a919-960ee860b00f"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6300));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("5d22874b-5834-40a2-81be-bf5100eb845d"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6295));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("c219bf33-28d0-4595-a25b-8018bd87e67a"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6293));

            migrationBuilder.UpdateData(
                table: "ProductCostMethod",
                keyColumn: "Id",
                keyValue: new Guid("e57e65d0-85a9-49c8-8463-3424a42e1f01"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6298));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("55f8c119-8df7-48ca-9709-3b1ad30e99e3"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6244));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("81ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6252));

            migrationBuilder.UpdateData(
                table: "TaxCategories",
                keyColumn: "Id",
                keyValue: new Guid("82ba4893-d891-4e3c-a5e7-ba13c697bf97"),
                column: "CreatedAt",
                value: new DateTime(2025, 6, 9, 17, 42, 42, 329, DateTimeKind.Utc).AddTicks(6258));
        }
    }
}
