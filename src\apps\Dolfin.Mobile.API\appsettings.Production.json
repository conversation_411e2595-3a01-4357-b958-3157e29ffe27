{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Dolfin.Framework.Repository.Implementations.CacheService": "Information", "Dolfin.Mobile.API.Controllers.AuthController": "Information"}}, "SwaggerSettings": {"Enabled": false}, "CorsSettings": {"AllowedOrigins": ["https://app.dolfin.my", "https://dolfin.my"]}, "CacheSettings": {"DefaultAbsoluteExpirationMinutes": 30, "DefaultSlidingExpirationMinutes": 15, "UserCacheExpirationMinutes": 30}, "JwtSettings": {"ExpirationMinutes": 30, "UseCookieAuthentication": false}, "AllowedHosts": "*"}