using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Data.Model;
using Dolfin.Framework.Data.Utils;
using Dolfin.Mobile.API.Connector;
using Dolfin.Mobile.API.Data;
using Dolfin.Mobile.API.Extensions;
using Dolfin.Mobile.API.Infrastructure;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Scheduler;
using Dolfin.Mobile.API.Services;
using Hangfire;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.EntityFrameworkCore;
using Hangfire.PostgreSql;
using Dolfin.Mobile.API.Models.Request;
using Hangfire.Server;
using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Middleware;
using Dolfin.Utility.Utils;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Dolfin.Framework.Repository.Common;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Framework.Repository.Implementations;
using Dolfin.Mobile.API.Repositories;
using Dolfin.Mobile.API.Repositories.Interfaces;

var builder = WebApplication.CreateBuilder(args);

#region Auto Mapper
builder.Services.AddAutoMapper(options => options.AddProfile<MappingProfile>());
#endregion

#region CORS Configuration
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        var allowedOrigins = builder.Configuration.GetSection("CorsSettings:AllowedOrigins").Get<string[]>() ?? new[] { "http://localhost:3000" };
        policy.WithOrigins(allowedOrigins)
              .AllowAnyMethod()
              .AllowAnyHeader()
              .AllowCredentials();
    });
});
#endregion

builder.Services.AddHttpContextAccessor();
builder.Services.AddHttpClient();

#region Service Registration
// Add memory cache
builder.Services.AddMemoryCache();
builder.Services.Configure<CacheSettings>(builder.Configuration.GetSection("CacheSettings"));

// Register cache service for API
builder.Services.AddScoped<ICacheService, CacheService>();

// Register repository services
builder.Services.AddRepositoryServices(builder.Configuration);

builder.Services.AddTransient<IEmailConnector, EmailConnector>();
builder.Services.AddTransient<Dolfin.Mobile.API.Services.IEmailSender, EmailSender>();
builder.Services.AddScoped<IAuthService, AuthService>();
builder.Services.AddSingleton<LoginAccount>();
builder.Services.AddScoped<ISettingsService, SettingsService>();
builder.Services.AddScoped<IUserService, UserService>();
builder.Services.AddScoped<ICompanyService, CompanyService>();
builder.Services.AddScoped<IProductService, ProductService>();
builder.Services.AddScoped<IInventoryService, InventoryService>();
builder.Services.AddScoped<ITransactionService, TransactionService>();
builder.Services.AddScoped<IEinvoiceService, EInvoiceService>();
builder.Services.AddScoped<ICustomerService, CustomerService>();
builder.Services.AddScoped<IPrefixService, PrefixService>();
builder.Services.AddScoped<IFileUploadService, FileUploadService>();
builder.Services.AddSingleton<StandardMessage>();
builder.Services.AddScoped<IJwtService, JwtService>();
builder.Services.AddScoped<IRefreshTokenRepository, RefreshTokenRepository>();
builder.Services.AddScoped<ILookupService, LookupService>();
builder.Services.AddScoped<IPermissionService, PermissionService>();
builder.Services.AddScoped<PermissionSeeder>();
#endregion

var connectionString = builder.Configuration.GetConnectionString("DolfinConnectionString");

// Configure JWT settings
builder.Services.Configure<JwtSettings>(builder.Configuration.GetSection("JwtSettings"));
builder.Services.Configure<EInvoiceSettings>(builder.Configuration.GetSection("EInvoiceSettings"));
builder.Services.Configure<AWSS3Config>(builder.Configuration.GetSection("AWSS3"));
builder.Services.AddTransient<AmazonS3Service>();

#region PostgreSQL Hangfire connection
var jobConfiguration = builder.Configuration.GetSection("HangFireScheduler");

//Hangfire get and create connection
builder.Services.AddHangfire(config =>
{
    config.UsePostgreSqlStorage(connectionString, new PostgreSqlStorageOptions
    {
        PrepareSchemaIfNecessary = true
    });
});

builder.Services.AddScoped<JobConfigService>();
builder.Services.AddScoped<EmailSchedulerService>();

// Register Background Jobs
builder.Services.RegisterHostedProcesses(jobConfiguration);

// Configure Hangfire server with more worker threads
builder.Services.AddHangfireServer(options =>
{
    options.WorkerCount = 5; // Increase worker count
    options.Queues = new[] { "default" }; // Ensure default queue is processed
    options.ServerName = "DolfinHangfireServer"; // Give the server a name
});
#endregion

#region Register DbContext and provide ConnectionString
builder.Services.AddDbContext<DolfinDbContext>(db => db.UseNpgsql(connectionString), ServiceLifetime.Scoped);
#endregion

#region Identity Configuration
builder.Services.AddIdentity<ApplicationUser, ApplicationRole>()
    .AddEntityFrameworkStores<DolfinDbContext>()
    .AddDefaultTokenProviders();

//builder.Services.AddIdentityApiEndpoints<ApplicationUser>()
//    .AddEntityFrameworkStores<DolfinDbContext>(); // Ensure this line is included
#endregion

#region Authentication Configuration
// Get JWT settings from configuration
var jwtSettings = builder.Configuration.GetSection("JwtSettings");
var key = Encoding.ASCII.GetBytes(jwtSettings["SecretKey"]);

// Configure authentication
builder.Services.AddAuthentication(options =>
{
    // Default to JWT for API requests, but allow Cookie auth as well
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options =>
{
    options.Cookie.Name = "Dolfin.Auth";
    options.Cookie.HttpOnly = true;
    options.ExpireTimeSpan = TimeSpan.FromMinutes(60);
    options.SlidingExpiration = true;
    options.LoginPath = "/api/auth/login";
    options.LogoutPath = "/api/auth/logout";
})
.AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuer = true,
        ValidateAudience = true,
        ValidateLifetime = true,
        ValidateIssuerSigningKey = true,
        ValidIssuer = jwtSettings["Issuer"],
        ValidAudience = jwtSettings["Audience"],
        IssuerSigningKey = new SymmetricSecurityKey(key),
        // Add some clock skew to prevent token expiration issues
        ClockSkew = TimeSpan.FromMinutes(5)
    };

    // Enable debugging
    options.Events = new JwtBearerEvents
    {
        OnAuthenticationFailed = context =>
        {
            Console.WriteLine("OnAuthenticationFailed: " + context.Exception.Message);
            return Task.CompletedTask;
        },
        OnTokenValidated = context =>
        {
            Console.WriteLine("OnTokenValidated: " + context.SecurityToken);
            return Task.CompletedTask;
        },
        OnMessageReceived = context =>
        {
            // If no token is present in the context, try to get it from Authorization header or cookie
            if (string.IsNullOrEmpty(context.Token))
            {
                context.Token = TokenUtils.GetAccessToken(context.HttpContext);
            }
            return Task.CompletedTask;
        },
        OnChallenge = context =>
        {
            Console.WriteLine("OnChallenge: " + context.AuthenticateFailure?.Message);
            return Task.CompletedTask;
        }
    };
});
#endregion

// Add services to the container.
builder.Services.AddControllers()
    .AddJsonOptions(options =>
    {
        // Configure JSON serialization to handle circular references
        options.JsonSerializerOptions.ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles;
        options.JsonSerializerOptions.DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.Never;
    });
builder.Services.AddEndpointsApiExplorer();

// Configure Swagger based on settings
builder.Services.ConfigureSwagger();

var app = builder.Build();

// Check if Swagger is enabled in configuration
var swaggerEnabled = app.Configuration.GetValue<bool>("SwaggerSettings:Enabled");
if (swaggerEnabled)
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

// Configure Hangfire Dashboard with options
app.UseHangfireDashboard("/hangfire", new DashboardOptions
{
    DashboardTitle = "Dolfin Hangfire Dashboard",
    DisplayStorageConnectionString = false,
    IsReadOnlyFunc = context => false // Allow all operations in development
});

// Ensure Hangfire jobs are properly configured
app.Logger.LogInformation("Hangfire jobs configured and ready to run");


app.UseHttpsRedirection();

// Use CORS middleware
app.UseCors("AllowFrontend");

// Use token extraction middleware
app.UseTokenExtraction();

app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();

// Seed permissions
//using (var scope = app.Services.CreateScope())
//{
//    var permissionSeeder = scope.ServiceProvider.GetRequiredService<PermissionSeeder>();
//    await permissionSeeder.SeedPermissionsAsync();
//}

app.Run();
