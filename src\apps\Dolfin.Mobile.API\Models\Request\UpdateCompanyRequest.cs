using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models.Request
{
    public class UpdateCompanyRequest
    {
        [JsonIgnore]
        public Guid Id { get; set; }
        public string? Code { get; set; }
        public string? Name { get; set; }
        public string? RegNo { get; set; }
        public string? SstNo { get; set; }
        public Guid? DefaultSalesTaxNoId { get; set; }
        public Guid? DefaultServiceTaxNoId { get; set; }
        public string? TinNo { get; set; }
        public string? Logo { get; set; }
        public bool? IsBranchSameProduct { get; set; }
        public bool? IsEnableInventory { get; set; }
        public bool? IsRoundingAdjustment { get; set; }
        public bool? IsEInvoiceSubmitAuto { get; set; }
        public int? ConsolidatePay { get; set; }
        public decimal? ServiceCharge { get; set; }
        public string? WebSiteUrl { get; set; }
        public string? EInvoiceClientId { get; set; }
        public string? EInvoiceClientSecret { get; set; }
        public Guid? CurrencyId { get; set; }
        public required Guid TaxCategoryId { get; set; }
        public Guid? MsicId { get; set; }
    }
}
