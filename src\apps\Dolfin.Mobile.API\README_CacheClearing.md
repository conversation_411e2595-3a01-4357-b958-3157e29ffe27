# Cache Clearing Implementation and Troubleshooting

## Overview

This document explains the improved cache clearing mechanism implemented to resolve issues where company profile data persists after logout in production environments.

## Problem Description

The original issue was that after logout, users could still see cached company profile data when logging back in, particularly in production environments. This was caused by:

1. **Reflection-based cache clearing failures** - The original `ClearAll()` method used reflection to access internal MemoryCache fields, which could fail silently in different .NET runtime versions or server environments.

2. **Limited error handling** - No fallback mechanisms when primary cache clearing failed.

3. **Insufficient logging** - Limited visibility into cache clearing success/failure.

4. **Single-strategy approach** - Only one method attempted for cache clearing.

## Solution Implementation

### 1. Multi-Strategy Cache Clearing

The new implementation uses multiple strategies in sequence:

#### Strategy 1: Enhanced Reflection-Based Clearing
- Tries multiple reflection approaches for different .NET versions
- `ClearUsingCoherentState()` - Original method using `_coherentState` field
- `ClearUsingEntriesField()` - Alternative using `_entries` field  
- `ClearUsingStoreField()` - Alternative using `_store` field

#### Strategy 2: User-Specific Cache Clearing
- Targets specific user cache keys when user ID is available
- Clears `CurrentUser_{userId}` and related user-specific entries

#### Strategy 3: Company-Specific Cache Clearing
- Attempts to determine user's company from claims
- Clears company-specific cache entries like `Company_{companyId}`

#### Strategy 4: Garbage Collection
- Forces garbage collection to help clear any remaining references

### 2. Comprehensive Logging

Enhanced logging at multiple levels:
- **Information**: Overall process status and success counts
- **Warning**: Individual strategy failures
- **Error**: Complete process failures
- **Debug**: Detailed operation steps

### 3. New Cache Service Methods

Added to `ICacheService` interface:
```csharp
void ClearUserCache(string userId);
void ClearCompanyCache(Guid companyId);
```

## Configuration

### Production Settings

Created `appsettings.Production.json` with:
- Reduced cache expiration times (30/15 minutes vs 60/30)
- Enhanced logging for cache operations
- Disabled Swagger for production

### Cache Settings Explanation

```json
{
  "CacheSettings": {
    "DefaultAbsoluteExpirationMinutes": 30,  // Cache expires after 30 minutes regardless of usage
    "DefaultSlidingExpirationMinutes": 15,   // Cache expires after 15 minutes of inactivity
    "UserCacheExpirationMinutes": 30         // User-specific cache expiration
  }
}
```

## Monitoring and Troubleshooting

### Log Messages to Monitor

1. **Successful Cache Clearing**:
   ```
   Cache clearing completed. Successful operations: 4/4
   ```

2. **Partial Failures**:
   ```
   Some cache clearing operations failed during logout. Successful: 2/4
   ```

3. **Complete Failures**:
   ```
   All cache clearing strategies failed during logout
   ```

### Troubleshooting Steps

#### 1. Check Application Logs

On the server, check the application logs:
```bash
sudo journalctl -u kestrel-dolfin-api.service -f | grep -i cache
```

#### 2. Verify Cache Settings

Ensure the production configuration is being used:
```bash
# Check if appsettings.Production.json exists
ls -la /var/app/dolfin-api/appsettings.Production.json

# Verify ASPNETCORE_ENVIRONMENT is set to Production
sudo systemctl show kestrel-dolfin-api.service --property=Environment
```

#### 3. Monitor Cache Operations

Look for these specific log entries during logout:
- "Starting comprehensive cache clearing for logout"
- "Successfully executed ClearAll() cache operation"
- "Cache clearing completed"

#### 4. Test Cache Clearing

To test if cache clearing is working:
1. Login as a user
2. Access company profile (this caches the data)
3. Logout (should clear cache)
4. Login again
5. Check if fresh data is loaded (should see database queries in logs)

### Common Issues and Solutions

#### Issue 1: Reflection Methods Failing
**Symptoms**: Logs show "Reflection approach failed, trying next method"
**Solution**: The system automatically tries multiple reflection approaches. If all fail, it falls back to targeted cache clearing.

#### Issue 2: User ID Not Available
**Symptoms**: Logs show "Cannot clear company cache: userId is null or empty"
**Solution**: Ensure JWT tokens include user identification claims.

#### Issue 3: Company ID Not in Claims
**Symptoms**: "Could not determine company ID from user claims"
**Solution**: Verify that company information is included in JWT token claims during login.

## Performance Considerations

### Memory Usage
- Garbage collection is forced after cache clearing
- This may cause brief performance impact but ensures thorough cleanup

### Logging Volume
- Enhanced logging may increase log volume
- Consider log rotation and retention policies

### Cache Expiration
- Shorter cache times in production reduce memory usage
- May slightly increase database queries but ensures data freshness

## Future Improvements

### Distributed Cache Consideration
For multi-server deployments, consider implementing:
- Redis distributed cache
- Pattern-based cache key clearing
- Cross-server cache invalidation

### Cache Key Tracking
Implement a cache key registry to:
- Track all active cache keys
- Enable complete enumeration for clearing
- Provide better fallback mechanisms

## Testing

### Unit Tests
Test the new cache clearing methods:
```csharp
[Test]
public void ClearUserCache_ShouldRemoveUserSpecificEntries()
{
    // Test implementation
}

[Test]
public void ClearAll_ShouldHandleReflectionFailures()
{
    // Test fallback mechanisms
}
```

### Integration Tests
Test the complete logout flow:
1. Login and cache data
2. Logout with cache clearing
3. Verify cache is cleared
4. Login again and verify fresh data load
