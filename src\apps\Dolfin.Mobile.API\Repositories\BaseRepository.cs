using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Implementations;
using Dolfin.Framework.Repository.Interfaces;
using Microsoft.Extensions.Logging;

namespace Dolfin.Mobile.API.Repositories
{
    /// <summary>
    /// Base repository implementation for the API project
    /// </summary>
    /// <typeparam name="T">Entity type</typeparam>
    public class BaseRepository<T> : Repository<T, DolfinDbContext> where T : class
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public BaseRepository(
            DolfinDbContext context,
            ICacheService cacheService,
            ILogger<BaseRepository<T>> logger)
            : base(context, cacheService, logger)
        {
        }
    }
}