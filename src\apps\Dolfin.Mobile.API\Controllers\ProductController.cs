﻿using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Data.Model;
using Dolfin.Framework.Data.Utils;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Infrastructure;
using Dolfin.Mobile.API.Models.Dto;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Reflection;
using static Dolfin.Mobile.API.Constants.Constants;
using static Dolfin.Utility.Enum.Enums;
using ValidationException = Dolfin.Utility.Utils.ExceptionHandler.ValidationException;

namespace Dolfin.Mobile.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ProductController : ControllerCore
    {
        private readonly StandardMessage _standardMessage;
        private readonly ILogger<ProductController> _logger;
        private readonly IProductService _productService;
        private readonly IUserService _userService;
        private readonly ICompanyService _companyService;
        private readonly IInventoryService _inventoryService;
        private readonly AmazonS3Service _amazonS3Service;
        private readonly IFileUploadService _fileUploadService;
        private readonly IMapper _mapper;
        private readonly DbContextOptions<DolfinDbContext> _dbContextOptions;

        public ProductController(
            ILogger<ProductController> logger,
            IProductService productService,
            IUserService userService,
            ICompanyService companyService,
            IInventoryService inventoryService,
            AmazonS3Service amazonS3Service,
            IFileUploadService fileUploadService,
            IMapper mapper,
            DbContextOptions<DolfinDbContext> dbContextOptions)
        {
            _standardMessage = new StandardMessage();
            _logger = logger;
            _mapper = mapper;
            _productService = productService;
            _userService = userService;
            _companyService = companyService;
            _inventoryService = inventoryService;
            _amazonS3Service = amazonS3Service;
            _fileUploadService = fileUploadService;
            _dbContextOptions = dbContextOptions;
        }

        #region  Product Category
        [HttpGet("ProductCategory")]
        public async Task<IActionResult> GetProductCategoryList([FromQuery] Pagination pagination, [FromQuery] CommonFilterList commonFilter)
        {
            var response = await _productService.GetProductCategoryList(pagination, commonFilter);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<ProductCategoryDto>, PagedList<ProductCategory>>(_mapper, response, pagination, PagedList<ProductCategory>.PagedMetadata(response));
        }

        [HttpGet("ProductCategory/Get/{productCategoryId}")]
        public async Task<IActionResult> GetProductCategory(Guid productCategoryId)
        {
            if (productCategoryId == null || productCategoryId == Guid.Empty)
            {
                return BadRequest(new { Message = "Product Category ID is required." });
            }

            var response = await _productService.GetProductCategoryByGuid(productCategoryId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ProductCategoryDto, ProductCategory>(_mapper, response);
        }


        [HttpPost("ProductCategory/Create")]
        public async Task<IActionResult> CreateProductCategory([FromBody] ProductCategoryRequest productCategoryRequest)
        {
            var response = await _productService.InsertProductCategory(productCategoryRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }


        [HttpPut("ProductCategory/Update/{productCategoryId}")]
        public async Task<IActionResult> UpdateProductCategory(Guid productCategoryId, [FromBody] UpdateProductCategoryRequest updateProductCategoryRequest)
        {
            updateProductCategoryRequest.Id = productCategoryId;
            var response = await _productService.UpdateProductCategory(updateProductCategoryRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPost("ProductCategory/Delete")]
        public async Task<IActionResult> DeleteProductCategory(Guid productCategoryId)
        {
            var userAccessValidatation = await _userService.UserAccessValidatation(true);
            if (!userAccessValidatation.IsSuccessful)
                return ActionResultResponse<NoResult, ResultId>(_mapper, userAccessValidatation);

            var response = await _productService.DeleteProductCategory(productCategoryId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }
        #endregion

        #region  Product
        [HttpGet("")]
        [RequirePermission(Permissions.Product.View)]
        public async Task<IActionResult> GetProductList([FromQuery] Pagination pagination, [FromQuery] CommonFilterList commonFilter)
        {
            // Get products from the service
            var response = await _productService.GetProductList(pagination, commonFilter);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                return ActionResultResponse<List<ProductDto>, PagedList<Product>>(_mapper, response, pagination, PagedList<Product>.PagedMetadata(response));
            }

            // Map products to DTOs - PagedList inherits from List<T> so we can access items directly
            var productDtos = _mapper.Map<List<ProductDto>>(response.Result);

            // Fetch images for each product
            foreach (var productDto in productDtos)
            {
                try
                {
                    // Get images for this product using the FileUploadService
                    var imagesResponse = await _fileUploadService.GetFilesByReferenceId(
                        productDto.Id,
                        GetConstantName(UPLOAD_MODULE.PRODUCT));

                    if (imagesResponse.IsSuccessful && imagesResponse.Result != null && imagesResponse.Result.Count > 0)
                    {
                        // Map FileUploadResponse to FileUploadDto and add to the product's Images collection
                        productDto.Images = _mapper.Map<List<FileUploadDto>>(imagesResponse.Result);
                    }
                }
                catch (Exception ex)
                {
                    // Log the error but don't fail the entire request
                    _logger.LogError(ex, $"Error fetching images for product {productDto.Id}");
                }
            }

            // Create a new paged list with the enhanced DTOs
            // Use the ToPagedList factory method since PagedList constructor is not directly accessible
            var enhancedPagedList = PagedList<ProductDto>.ToPagedList(
                productDtos,
                response.Result.TotalCount,
                response.Result.CurrentPage,
                response.Result.PageSize
            );

            // Create a new response with the enhanced paged list
            var enhancedResponse = new BaseResponse<PagedList<ProductDto>>
            {
                IsSuccessful = response.IsSuccessful,
                Result = enhancedPagedList,
                Exception = response.Exception
            };

            return ActionResultResponse<List<ProductDto>, PagedList<ProductDto>>(
                _mapper,
                enhancedResponse,
                pagination,
                PagedList<ProductDto>.PagedMetadata(enhancedResponse));
        }

        [HttpGet("Get/{productId}")]
        [RequirePermission(Permissions.Product.View)]
        public async Task<IActionResult> GetProductByGuid(Guid productId)
        {
            if (productId == null || productId == Guid.Empty)
            {
                return BadRequest(new { Message = "Product ID is required." });
            }

            // Get the product from the service
            var response = await _productService.GetProductByGuid(productId);
            if (!response.IsSuccessful || response.Result == null)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                return ActionResultResponse<ProductDto, Product>(_mapper, response);
            }

            // Map the product to DTO
            var productDto = _mapper.Map<ProductDto>(response.Result);

            try
            {
                // Get images for this product using the FileUploadService
                var imagesResponse = await _fileUploadService.GetFilesByReferenceId(
                    productId,
                    GetConstantName(UPLOAD_MODULE.PRODUCT));

                if (imagesResponse.IsSuccessful && imagesResponse.Result != null && imagesResponse.Result.Count > 0)
                {
                    // Map FileUploadResponse to FileUploadDto and add to the product's Images collection
                    productDto.Images = _mapper.Map<List<FileUploadDto>>(imagesResponse.Result);
                }
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the entire request
                _logger.LogError(ex, $"Error fetching images for product {productId}");
            }

            // Create a new response with the enhanced DTO
            var enhancedResponse = new BaseResponse<ProductDto>
            {
                IsSuccessful = response.IsSuccessful,
                Result = productDto,
                Exception = response.Exception
            };

            return ActionResultResponse<ProductDto, ProductDto>(_mapper, enhancedResponse);
        }

        [HttpPost("Create")]
        [RequirePermission(Permissions.Product.Create)]
        public async Task<IActionResult> CreateProduct([FromBody] ProductRequest productRequest)
        {
            var response = new BaseResponse<ResultId> { IsSuccessful = true };
            DolfinDbContext dbContext = null;

            try
            {
                // Validate CustomSalesTaxNo if provided
                if (productRequest.CustomSalesTaxNo.HasValue)
                {
                    var salesTaxResponse = await _companyService.GetTaxRateByGuid(productRequest.CustomSalesTaxNo.Value);
                    if (!salesTaxResponse.IsSuccessful || salesTaxResponse.Result == null)
                    {
                        throw new ValidationException($"Custom Sales Tax with ID {productRequest.CustomSalesTaxNo.Value} not found.");
                    }
                }

                // Validate CustomServiceTaxNo if provided
                if (productRequest.CustomServiceTaxNo.HasValue)
                {
                    var serviceTaxResponse = await _companyService.GetTaxRateByGuid(productRequest.CustomServiceTaxNo.Value);
                    if (!serviceTaxResponse.IsSuccessful || serviceTaxResponse.Result == null)
                    {
                        throw new ValidationException($"Custom Service Tax with ID {productRequest.CustomServiceTaxNo.Value} not found.");
                    }
                }

                // Validate ProductCategoryId
                var productCategoryResponse = await _productService.GetProductCategoryByGuid(productRequest.ProductCategoryId);
                if (!productCategoryResponse.IsSuccessful || productCategoryResponse.Result == null)
                {
                    throw new ValidationException($"Product Category with ID {productRequest.ProductCategoryId} not found.");
                }

                // Validate ProductCostMethodId
                var productCostMethodResponse = await _productService.GetProductCostMethodByGuid(productRequest.ProductCostMethodId);
                if (!productCostMethodResponse.IsSuccessful || productCostMethodResponse.Result == null)
                {
                    throw new ValidationException($"Product Cost Method with ID {productRequest.ProductCostMethodId} not found.");
                }

                // Validate ClassificationId if provided
                if (productRequest.ClassificationId.HasValue && productRequest.ClassificationId.Value != Guid.Empty)
                {
                    var classificationResponse = await _productService.GetClassificationByGuid(productRequest.ClassificationId.Value);
                    if (!classificationResponse.IsSuccessful || classificationResponse.Result == null)
                    {
                        throw new ValidationException($"Classification with ID {productRequest.ClassificationId} not found.");
                    }
                }

                // Create a database context for transaction
                dbContext = new DolfinDbContext(_dbContextOptions);
                var transaction = await dbContext.Database.BeginTransactionAsync();

                try
                {
                    // Get current user and company information
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(productRequest);
                    var currentUser = getCurrentUser.Item1;
                    productRequest = getCurrentUser.Item2;
                    var companyId = currentUser.CompanyId;
                    Guid? branchId = null;
                    var isBranchSameProduct = false;

                    // Determine branch settings based on user role
                    if (!(currentUser.Branch?.IsHq ?? false))
                    {
                        // Not HQ, only allow create own inventory product
                        branchId = currentUser.BranchId;
                    }
                    else
                    {
                        // Only HQ has access to allow system to create for other branch inventory products
                        isBranchSameProduct = currentUser.Company?.IsBranchSameProduct ?? false;
                    }

                    //if (productRequest.ProductFormFile != null)
                    //{
                    //    string filePath = AmazonS3Service.ConstructDirectory(UPLOAD_DIRECTORY.PRODUCT, companyId.ToString(), FILE_TYPE.Files.GetDescription(), UPLOAD_MODULE.PRODUCT);
                    //    var fileResult = await _amazonS3Service.UploadObject(new UploadFileRequest()
                    //    {
                    //        FolderPath = filePath,
                    //        File = productRequest.ProductFormFile
                    //    });

                    //    if (!fileResult.Success)
                    //    {
                    //        throw new Exception(fileResult.FileName);
                    //    }
                    //    productRequest.Image = fileResult.FullPath;
                    //}

                    // Step 1: Create the product
                    response = await _productService.InsertProduct(productRequest, currentUser, dbContext);
                    if (!response.IsSuccessful)
                    {
                        throw new Exception(response.Exception);
                    }

                    if (response.Result?.Id == null)
                    {
                        throw new ValidationException("Product ID is null after creation.");
                    }

                    Guid productId = response.Result.Id.Value;

                    // Step 2: Create ProductUOMs if provided
                    if (productRequest.ProductUOMInputRequest != null && productRequest.ProductUOMInputRequest.Count > 0)
                    {
                        // Validate that exactly one UOM is marked as main UOM
                        var mainUomCount = productRequest.ProductUOMInputRequest.Count(x => x.IsMainUom);
                        if (mainUomCount == 0)
                        {
                            throw new ValidationException("At least one UOM must be marked as the main UOM.");
                        }
                        else if (mainUomCount > 1)
                        {
                            throw new ValidationException("Only one UOM can be marked as the main UOM.");
                        }

                        // Process each ProductUOM request
                        foreach (var productUOMRequest in productRequest.ProductUOMInputRequest)
                        {
                            // Validate Fraction is at least 1
                            if (productUOMRequest.Fraction < 1)
                            {
                                throw new ValidationException($"Fraction must be at least 1. Current value: {productUOMRequest.Fraction}");
                            }

                            // Validate FractionQty is at least 1 if provided
                            if (productUOMRequest.ProductPriceInputRequest != null &&
                                productUOMRequest.ProductPriceInputRequest.FractionQty.HasValue &&
                                productUOMRequest.ProductPriceInputRequest.FractionQty.Value < 1)
                            {
                                throw new ValidationException($"FractionQty must be at least 1. Current value: {productUOMRequest.ProductPriceInputRequest.FractionQty.Value}");
                            }

                            // Validate UomPrimaryId
                            var uomPrimary = await _productService.GetUOMByGuid(productUOMRequest.UomPrimaryId);
                            if (!uomPrimary.IsSuccessful || uomPrimary.Result == null)
                            {
                                throw new ValidationException($"UOM Primary with ID {productUOMRequest.UomPrimaryId} not found: {uomPrimary.Exception}");
                            }

                            // Generate the ProductUOM code
                            var productUOMCode = productRequest.Code + uomPrimary.Result.Code;

                            // No need to check for existing ProductUOM when creating a new product
                            // The code should be unique since we're using a new product code

                            // Calculate fraction total - default to 1.0m for primary UOM
                            decimal fractionTotal = 1.0m;

                            // Handle secondary UOM if provided
                            if (productUOMRequest.UomSecondaryId.HasValue)
                            {
                                var uomSecondary = await _productService.GetUOMByGuid(productUOMRequest.UomSecondaryId.Value);
                                if (!uomSecondary.IsSuccessful || uomSecondary.Result == null)
                                {
                                    throw new ValidationException($"UOM Secondary with ID {productUOMRequest.UomSecondaryId} not found: {uomSecondary.Exception}");
                                }

                                // Get ProductUOM by ProductId and UomId (where UomId is the secondary UOM's ID)
                                var existingProductUOM = await _productService.GetProductUOMByProductAndUomId(
                                    productId: productId,
                                    uomId: uomSecondary.Result.Id,
                                    dbContextRollback: dbContext
                                );

                                if (!existingProductUOM.IsSuccessful || existingProductUOM.Result == null)
                                {
                                    throw new ValidationException($"Could not find valid ProductUOM's primary UOM for secondary UOM: {existingProductUOM.Exception}");
                                }

                                // Use the FractionTotal from the existing ProductUOM
                                fractionTotal = existingProductUOM.Result.FractionTotal;
                            }

                            // Create the ProductUOM request
                            var newProductUOMRequest = new ProductUOMRequest
                            {
                                Code = productUOMCode,
                                Name = productUOMCode,
                                ProductId = productId,
                                UomPrimaryId = productUOMRequest.UomPrimaryId,
                                UomSecondaryId = productUOMRequest.UomSecondaryId,
                                Barcode = productUOMRequest.Barcode,
                                IsMainUom = productUOMRequest.IsMainUom,
                                IsPriceFollowUomMainId = productUOMRequest.IsPriceFollowUomMainId,
                                Cost = productUOMRequest.Cost,
                                PreviousCost = productUOMRequest.PreviousCost,
                                OrderMinQty = productUOMRequest.OrderMinQty,
                                OrderMaxQty = productUOMRequest.OrderMaxQty,
                                PriceEditable = productUOMRequest.PriceEditable,
                                MinEditPrice = productUOMRequest.MinEditPrice,
                                MaxEditPrice = productUOMRequest.MaxEditPrice,
                                Fraction = productUOMRequest.Fraction,
                                FractionTotal = fractionTotal * productUOMRequest.Fraction
                            };

                            // Insert the ProductUOM
                            var insertProductUOMResult = await _productService.InsertProductUOM(newProductUOMRequest, dbContext);
                            if (!insertProductUOMResult.IsSuccessful)
                            {
                                throw new ValidationException($"Failed to create ProductUOM: {insertProductUOMResult.Exception}");
                            }

                            _logger.LogInformation($"ProductUOM {insertProductUOMResult.Result?.Id} created for product {productId}");

                            // Create ProductPrice if provided
                            if (productUOMRequest.ProductPriceInputRequest != null)
                            {
                                var productPriceRequest = new ProductPriceRequest
                                {
                                    FractionQty = productUOMRequest.ProductPriceInputRequest.FractionQty ?? 1.00m,
                                    Price = productUOMRequest.ProductPriceInputRequest.Price,
                                    EffectiveAt = productUOMRequest.ProductPriceInputRequest.EffectiveAt ?? DateTime.UtcNow,
                                    Remark = productUOMRequest.ProductPriceInputRequest.Remark,
                                    ProductUOMId = insertProductUOMResult.Result?.Id ?? throw new ValidationException("ProductUOM ID is null after creation.")
                                };

                                var insertProductPriceResult = await _productService.InsertProductPrice(productPriceRequest, dbContext);
                                if (!insertProductPriceResult.IsSuccessful)
                                {
                                    throw new Exception($"Failed to create ProductPrice: {insertProductPriceResult.Exception}");
                                }

                                _logger.LogInformation($"ProductPrice {insertProductPriceResult.Result?.Id} created for ProductUOM {insertProductUOMResult.Result?.Id}");
                            }
                        }
                    }
                    else
                    {
                        _logger.LogWarning($"No ProductUOM records provided for product {productId}");
                    }

                    // Step 3: Create InventoryProducts
                    if (companyId == null)
                    {
                        throw new ValidationException("Company ID is required to create inventory products.");
                    }
                    var inventoryListByCompanyBranchResult = await _inventoryService.GetInventoryListByCompanyBranch(companyId.Value, branchId);
                    if (!inventoryListByCompanyBranchResult.IsSuccessful)
                        throw new ValidationException(inventoryListByCompanyBranchResult.Exception);
                    if (inventoryListByCompanyBranchResult.Result == null || inventoryListByCompanyBranchResult.Result.Count == 0)
                        throw new ValidationException("No inventory records found for the company/branch.");

                    // Filter inventory list based on branch settings
                    var inventoryListByCompanyBranchList = inventoryListByCompanyBranchResult.Result
                        .Where(x => isBranchSameProduct || (!isBranchSameProduct && x.BranchId == branchId))
                        .ToList();

                    if (inventoryListByCompanyBranchList.Count == 0)
                    {
                        throw new ValidationException("No applicable inventory records found for the product.");
                    }

                    // Create inventory products for each applicable inventory
                    foreach (var inventory in inventoryListByCompanyBranchList)
                    {
                        var inventoryProductRequest = new InventoryProductRequest
                        {
                            ProductId = productId,
                            InventoryId = inventory.Id,
                            BranchId = branchId
                        };

                        var inventoryProductResult = await _inventoryService.InsertInventoryProduct(inventoryProductRequest, dbContext);
                        if (!inventoryProductResult.IsSuccessful)
                        {
                            throw new ValidationException($"Failed to create inventory product: {inventoryProductResult.Exception}");
                        }

                        _logger.LogInformation($"Inventory product {inventoryProductResult.Result?.Id} created for product {productId}");
                    }

                    // Commit the transaction if everything succeeded
                    await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    // Rollback the transaction if any step failed
                    await transaction.RollbackAsync();
                    throw ex; // Re-throw to be caught by the outer catch block
                }
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            finally
            {
                // Dispose of the database context if it was created
                if (dbContext != null)
                {
                    await dbContext.DisposeAsync();
                }
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPut("Update/{productId}")]
        [RequirePermission(Permissions.Product.Update)]
        public async Task<IActionResult> UpdateProduct(Guid productId, [FromBody] UpdateProductRequest updateProductRequest)
        {
            var response = new BaseResponse<ResultId> { IsSuccessful = true };
            DolfinDbContext dbContext = null;

            try
            {
                // Create a database context for transaction
                dbContext = new DolfinDbContext(_dbContextOptions);
                var transaction = await dbContext.Database.BeginTransactionAsync();

                try
                {
                    // Validate productId
                    if (productId == Guid.Empty)
                    {
                        throw new ValidationException("Product ID is required.");
                    }
                    updateProductRequest.Id = productId;

                    // Get current user and update request body with company info if needed
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(updateProductRequest);
                    var currentUser = getCurrentUser.Item1;
                    updateProductRequest = getCurrentUser.Item2;
                    var companyId = currentUser.CompanyId;

                    // Product existence will be validated in the service method

                    // Validate CustomSalesTaxNo if provided
                    if (updateProductRequest.CustomSalesTaxNo.HasValue && updateProductRequest.CustomSalesTaxNo.Value != Guid.Empty)
                    {
                        var salesTaxResponse = await _companyService.GetTaxRateByGuid(updateProductRequest.CustomSalesTaxNo.Value);
                        if (!salesTaxResponse.IsSuccessful || salesTaxResponse.Result == null)
                        {
                            throw new ValidationException($"Custom Sales Tax with ID {updateProductRequest.CustomSalesTaxNo.Value} not found.");
                        }
                    }

                    // Validate CustomServiceTaxNo if provided
                    if (updateProductRequest.CustomServiceTaxNo.HasValue && updateProductRequest.CustomServiceTaxNo.Value != Guid.Empty)
                    {
                        var serviceTaxResponse = await _companyService.GetTaxRateByGuid(updateProductRequest.CustomServiceTaxNo.Value);
                        if (!serviceTaxResponse.IsSuccessful || serviceTaxResponse.Result == null)
                        {
                            throw new ValidationException($"Custom Service Tax with ID {updateProductRequest.CustomServiceTaxNo.Value} not found.");
                        }
                    }

                    // Validate ProductCategoryId if provided
                    if (updateProductRequest.ProductCategoryId.HasValue && updateProductRequest.ProductCategoryId.Value != Guid.Empty)
                    {
                        var productCategoryResponse = await _productService.GetProductCategoryByGuid(updateProductRequest.ProductCategoryId.Value);
                        if (!productCategoryResponse.IsSuccessful || productCategoryResponse.Result == null)
                        {
                            throw new ValidationException($"Product Category with ID {updateProductRequest.ProductCategoryId.Value} not found.");
                        }
                    }

                    // Validate ClassificationId if provided
                    if (updateProductRequest.ClassificationId.HasValue && updateProductRequest.ClassificationId.Value != Guid.Empty)
                    {
                        var classificationResponse = await _productService.GetClassificationByGuid(updateProductRequest.ClassificationId.Value);
                        if (!classificationResponse.IsSuccessful || classificationResponse.Result == null)
                        {
                            throw new ValidationException($"Classification with ID {updateProductRequest.ClassificationId} not found.");
                        }
                    }

                    // Validate date ranges if provided
                    if (updateProductRequest.AvailableStartAt.HasValue && updateProductRequest.AvailableEndAt.HasValue)
                    {
                        if (updateProductRequest.AvailableStartAt > updateProductRequest.AvailableEndAt)
                        {
                            throw new ValidationException("Available start date cannot be later than available end date.");
                        }
                    }

                    // Validate dimensions if provided
                    if (updateProductRequest.Weight.HasValue && updateProductRequest.Weight < 0)
                    {
                        throw new ValidationException("Weight cannot be negative.");
                    }

                    if (updateProductRequest.Length.HasValue && updateProductRequest.Length < 0)
                    {
                        throw new ValidationException("Length cannot be negative.");
                    }

                    if (updateProductRequest.Width.HasValue && updateProductRequest.Width < 0)
                    {
                        throw new ValidationException("Width cannot be negative.");
                    }

                    if (updateProductRequest.Height.HasValue && updateProductRequest.Height < 0)
                    {
                        throw new ValidationException("Height cannot be negative.");
                    }

                    //if (updateProductRequest.ProductFormFile != null)
                    //{
                    //    // Get the current image URL to delete if it exists
                    //    string deleteFileUrl = null;
                    //    var currentProduct = await _productService.GetProductByGuid(productId);
                    //    if (currentProduct.IsSuccessful && currentProduct.Result != null)
                    //    {
                    //        deleteFileUrl = currentProduct.Result.Image;
                    //    }

                    //    string filePath = AmazonS3Service.ConstructDirectory(UPLOAD_DIRECTORY.PRODUCT, companyId.ToString(), FILE_TYPE.Files.GetDescription(), UPLOAD_MODULE.PRODUCT);
                    //    var fileResult = await _amazonS3Service.UploadObject(new UploadFileRequest()
                    //    {
                    //        FolderPath = filePath,
                    //        File = updateProductRequest.ProductFormFile,
                    //        DeleteFileUrl = deleteFileUrl
                    //    });

                    //    if (!fileResult.Success)
                    //    {
                    //        throw new Exception(fileResult.FileName);
                    //    }
                    //    updateProductRequest.Image = fileResult.FullPath;
                    //}

                    // If all validations pass, proceed with updating the product
                    response = await _productService.UpdateProduct(updateProductRequest, currentUser, dbContext);
                    if (!response.IsSuccessful)
                    {
                        throw new Exception(response.Exception);
                    }

                    // Commit the transaction if everything succeeded
                    await transaction.CommitAsync();

                    _logger.LogInformation($"Product with ID {productId} updated successfully.");
                }
                catch (Exception ex)
                {
                    // Rollback the transaction if any step failed
                    await transaction.RollbackAsync();
                    throw ex; // Re-throw to be caught by the outer catch block
                }
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}: {ex.Message}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}: {ex.Message}");
            }
            finally
            {
                // Dispose of the database context if it was created
                if (dbContext != null)
                {
                    await dbContext.DisposeAsync();
                }
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPost("Delete")]
        [RequirePermission(Permissions.Product.Delete)]
        public async Task<IActionResult> DeleteProduct(Guid productId)
        {
            var userAccessValidatation = await _userService.UserAccessValidatation(true);
            if (!userAccessValidatation.IsSuccessful)
                return ActionResultResponse<NoResult, ResultId>(_mapper, userAccessValidatation);

            var response = await _productService.DeleteProduct(productId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }
        #endregion

        #region  Product UOM
        [HttpGet("ProductUOM")]
        public async Task<IActionResult> GetProductUOMList([FromQuery] Pagination pagination, [FromQuery] CommonFilterList commonFilter)
        {
            var response = await _productService.GetProductUOMList(pagination, commonFilter);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<ProductUOMDto>, PagedList<ProductUOM>>(_mapper, response, pagination, PagedList<ProductUOM>.PagedMetadata(response));
        }

        [HttpGet("ProductUOM/Get/{productUOMId}")]
        public async Task<IActionResult> GetProductUOM(Guid productUOMId)
        {
            if (productUOMId == null || productUOMId == Guid.Empty)
            {
                return BadRequest(new { Message = "Product UOM ID is required." });
            }

            var response = await _productService.GetProductUOMByGuid(productUOMId, null);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ProductUOMDto, ProductUOM>(_mapper, response);
        }

        [HttpPost("ProductUOM/Create")]
        public async Task<IActionResult> CreateProductUOM([FromBody] ProductUOMRequest productUOMRequest)
        {
            var response = new BaseResponse<ResultId> { IsSuccessful = true };
            DolfinDbContext dbContext = null;

            try
            {
                // Create a database context for the transaction
                dbContext = _productService.GetDbContext();
                var transaction = await dbContext.Database.BeginTransactionAsync();

                try
                {
                    // Validate UomPrimaryId
                    var uomPrimaryResponse = await _productService.GetUOMByGuid(productUOMRequest.UomPrimaryId);
                    if (!uomPrimaryResponse.IsSuccessful || uomPrimaryResponse.Result == null)
                    {
                        throw new ValidationException($"UOM Primary with ID {productUOMRequest.UomPrimaryId} not found.");
                    }

                    // Validate UomSecondaryId if provided
                    if (productUOMRequest.UomSecondaryId.HasValue)
                    {
                        var uomSecondaryResponse = await _productService.GetUOMByGuid(productUOMRequest.UomSecondaryId.Value);
                        if (!uomSecondaryResponse.IsSuccessful || uomSecondaryResponse.Result == null)
                        {
                            throw new ValidationException($"UOM Secondary with ID {productUOMRequest.UomSecondaryId.Value} not found.");
                        }
                    }

                    // Validate ProductId
                    var productResponse = await _productService.GetProductByGuid(productUOMRequest.ProductId);
                    if (!productResponse.IsSuccessful || productResponse.Result == null)
                    {
                        throw new ValidationException($"Product with ID {productUOMRequest.ProductId} not found.");
                    }

                    // Calculate fraction total - default to 1.0m for primary UOM
                    decimal fractionTotal = 1.0m;
                    //decimal fractionQty = 1.0m;
                    //decimal mainUOMPrice = 1.0m;

                    // Handle secondary UOM if provided
                    if (productUOMRequest.UomSecondaryId.HasValue)
                    {
                        var uomSecondary = await _productService.GetUOMByGuid(productUOMRequest.UomSecondaryId.Value);
                        if (!uomSecondary.IsSuccessful || uomSecondary.Result == null)
                        {
                            throw new ValidationException($"UOM Secondary with ID {productUOMRequest.UomSecondaryId} not found: {uomSecondary.Exception}");
                        }

                        // Get ProductUOM by ProductId and UomId (where UomId is the secondary UOM's ID)
                        var existingProductUOM = await _productService.GetProductUOMByProductAndUomId(
                            productId: productUOMRequest.ProductId,
                            uomId: uomSecondary.Result.Id,
                            dbContextRollback: dbContext
                        );

                        if (!existingProductUOM.IsSuccessful || existingProductUOM.Result == null)
                        {
                            throw new ValidationException($"Could not find valid ProductUOM's primary UOM for secondary UOM: {existingProductUOM.Exception}");
                        }

                        // Use the FractionTotal from the existing ProductUOM
                        fractionTotal = existingProductUOM.Result.FractionTotal;
                        //fractionQty = existingProductUOM.Result.EffectivedProductPrice.FractionQty;
                    }

                    // Update FractionTotal based on the calculated value
                    productUOMRequest.FractionTotal = fractionTotal * productUOMRequest.Fraction;

                    // Using ProductPrice property directly instead of ProductPriceId

                    // If all validations pass, proceed with creating the product UOM
                    response = await _productService.InsertProductUOM(productUOMRequest, dbContext);
                    if (!response.IsSuccessful)
                    {
                        throw new Exception(response.Exception);
                    }

                    _logger.LogInformation($"ProductUOM {response.Result?.Id} created successfully");

                    // Create ProductPrice if provided
                    if (productUOMRequest.ProductPrice != null)
                    {
                        var productPrice = productUOMRequest.ProductPrice.Price;
                        //if (productUOMRequest.IsPriceFollowUomMainId)
                        //{
                        //    productPrice = fractionTotal * fractionQty;
                        //}
                        var productPriceRequest = new ProductPriceRequest
                        {
                            Price = productPrice,
                            EffectiveAt = productUOMRequest.ProductPrice.EffectiveAt,
                            Remark = productUOMRequest.ProductPrice.Remark,
                            ProductUOMId = response.Result?.Id ?? throw new ValidationException("ProductUOM ID is null after creation.")
                        };

                        var insertProductPriceResult = await _productService.InsertProductPrice(productPriceRequest, dbContext);
                        if (!insertProductPriceResult.IsSuccessful)
                        {
                            throw new Exception($"Failed to create ProductPrice: {insertProductPriceResult.Exception}");
                        }

                        _logger.LogInformation($"ProductPrice {insertProductPriceResult.Result?.Id} created for ProductUOM {response.Result?.Id}");
                    }

                    // Commit the transaction if everything succeeded
                    await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    // Rollback the transaction if any step failed
                    await transaction.RollbackAsync();
                    throw ex; // Re-throw to be caught by the outer catch block
                }
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            finally
            {
                // Dispose of the database context if it was created
                if (dbContext != null)
                {
                    await dbContext.DisposeAsync();
                }
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPut("ProductUOM/Update/{productUOMId}")]
        public async Task<IActionResult> UpdateProductUOM(Guid productUOMId, [FromBody] UpdateProductUOMRequest updateProductUOMRequest)
        {
            var response = new BaseResponse<ResultId> { IsSuccessful = true };
            DolfinDbContext dbContext = null;

            try
            {
                // Create a database context for transaction
                dbContext = new DolfinDbContext(_dbContextOptions);
                var transaction = await dbContext.Database.BeginTransactionAsync();

                try
                {
                    updateProductUOMRequest.Id = productUOMId;

                    // Get the existing ProductUOM to validate
                    var existingProductUOM = await _productService.GetProductUOMByGuid(productUOMId, dbContext);
                    if (!existingProductUOM.IsSuccessful || existingProductUOM.Result == null)
                    {
                        throw new ValidationException($"Product UOM with ID {productUOMId} not found.");
                    }

                    // Using ProductPrice property directly instead of ProductPriceId

                    // If all validations pass, proceed with updating the product UOM
                    response = await _productService.UpdateProductUOM(updateProductUOMRequest, dbContext);
                    if (!response.IsSuccessful)
                    {
                        throw new Exception(response.Exception);
                    }

                    // Commit the transaction if everything succeeded
                    await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    // Rollback the transaction if any step failed
                    await transaction.RollbackAsync();
                    throw ex; // Re-throw to be caught by the outer catch block
                }
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            finally
            {
                // Dispose of the database context if it was created
                if (dbContext != null)
                {
                    await dbContext.DisposeAsync();
                }
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPost("ProductUOM/Delete")]
        public async Task<IActionResult> DeleteProductUOM(Guid productUOMId)
        {
            var userAccessValidatation = await _userService.UserAccessValidatation(true);
            if (!userAccessValidatation.IsSuccessful)
                return ActionResultResponse<NoResult, ResultId>(_mapper, userAccessValidatation);

            var response = await _productService.DeleteProductUOM(productUOMId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }
        #endregion

        #region  Product Cost Method
        [HttpGet("ProductCostMethod")]
        public async Task<IActionResult> GetProductCostMethodList([FromQuery] Pagination pagination, [ModelBinder(BinderType = typeof(CommonFilterListModelBinder))] CommonFilterList commonFilter)
        {
            var response = await _productService.GetProductCostMethodList(pagination, commonFilter);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<ProductCostMethodDto>, PagedList<ProductCostMethod>>(_mapper, response, pagination, PagedList<ProductCostMethod>.PagedMetadata(response));
        }

        [HttpGet("ProductCostMethod/Get/{productCostMethodId}")]
        public async Task<IActionResult> GetProductCostMethod(Guid productCostMethodId)
        {
            if (productCostMethodId == null || productCostMethodId == Guid.Empty)
            {
                return BadRequest(new { Message = "Product Cost Method ID is required." });
            }

            var response = await _productService.GetProductCostMethodByGuid(productCostMethodId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ProductCostMethodDto, ProductCostMethod>(_mapper, response);
        }

        [HttpPost("ProductCostMethod/Create")]
        public async Task<IActionResult> CreateProductCostMethod([FromBody] ProductCostMethodRequest productCostMethodRequest)
        {
            var response = await _productService.InsertProductCostMethod(productCostMethodRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPut("ProductCostMethod/Update/{productCostMethodId}")]
        public async Task<IActionResult> UpdateProductCostMethod(Guid productCostMethodId, [FromBody] UpdateProductCostMethodRequest updateProductCostMethodRequest)
        {
            updateProductCostMethodRequest.Id = productCostMethodId;
            var response = await _productService.UpdateProductCostMethod(updateProductCostMethodRequest);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPost("ProductCostMethod/Delete")]
        public async Task<IActionResult> DeleteProductCostMethod(Guid productCostMethodId)
        {
            var userAccessValidatation = await _userService.UserAccessValidatation(true);
            if (!userAccessValidatation.IsSuccessful)
                return ActionResultResponse<NoResult, ResultId>(_mapper, userAccessValidatation);

            var response = await _productService.DeleteProductCostMethod(productCostMethodId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }
        #endregion

        #region  UOM
        [HttpGet("UOM")]
        public async Task<IActionResult> GetUOMList([FromQuery] Pagination pagination, [FromQuery] CommonFilterList commonFilter)
        {
            var response = await _productService.GetUOMList(pagination, commonFilter);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<UOMDto>, PagedList<UOM>>(_mapper, response, pagination, PagedList<UOM>.PagedMetadata(response));
        }

        [HttpGet("UOM/Get/{uomId}")]
        public async Task<IActionResult> GetUOM(Guid uomId)
        {
            if (uomId == null || uomId == Guid.Empty)
            {
                return BadRequest(new { Message = "UOM ID is required." });
            }

            var response = await _productService.GetUOMByGuid(uomId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<UOMDto, UOM>(_mapper, response);
        }

        [HttpPost("UOM/Create")]
        public async Task<IActionResult> CreateUOM([FromBody] UOMRequest uomRequest)
        {
            var response = new BaseResponse<ResultId> { IsSuccessful = true };

            try
            {
                // Validate required fields
                if (string.IsNullOrWhiteSpace(uomRequest.Code))
                {
                    throw new ValidationException("UOM Code is required.");
                }

                if (string.IsNullOrWhiteSpace(uomRequest.Name))
                {
                    throw new ValidationException("UOM Name is required.");
                }

                // Validate UOMCategoryId
                if (uomRequest.UOMCategoryId == Guid.Empty)
                {
                    throw new ValidationException("UOM Category ID is required.");
                }

                var uomCategoryResponse = await _productService.GetUOMCategoryByGuid(uomRequest.UOMCategoryId);
                if (!uomCategoryResponse.IsSuccessful || uomCategoryResponse.Result == null)
                {
                    throw new ValidationException($"UOM Category with ID {uomRequest.UOMCategoryId} not found.");
                }

                // If all validations pass, proceed with creating the UOM
                response = await _productService.InsertUOM(uomRequest);
                if (!response.IsSuccessful)
                {
                    _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                }
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPut("UOM/Update/{uomId}")]
        public async Task<IActionResult> UpdateUOM(Guid uomId, [FromBody] UpdateUOMRequest updateUOMRequest)
        {
            var response = new BaseResponse<ResultId> { IsSuccessful = true };

            try
            {
                updateUOMRequest.Id = uomId;

                // Validate that the UOM exists
                var existingUOM = await _productService.GetUOMByGuid(uomId);
                if (!existingUOM.IsSuccessful || existingUOM.Result == null)
                {
                    throw new ValidationException($"UOM with ID {uomId} not found.");
                }

                // Validate UOMCategoryId if provided and not empty
                if (updateUOMRequest.UOMCategoryId != Guid.Empty)
                {
                    var uomCategoryResponse = await _productService.GetUOMCategoryByGuid(updateUOMRequest.UOMCategoryId);
                    if (!uomCategoryResponse.IsSuccessful || uomCategoryResponse.Result == null)
                    {
                        throw new ValidationException($"UOM Category with ID {updateUOMRequest.UOMCategoryId} not found.");
                    }
                }

                // If all validations pass, proceed with updating the UOM
                response = await _productService.UpdateUOM(updateUOMRequest);
                if (!response.IsSuccessful)
                {
                    _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                }
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPost("UOM/Delete")]
        public async Task<IActionResult> DeleteUOM(Guid uomId)
        {
            var userAccessValidatation = await _userService.UserAccessValidatation(true);
            if (!userAccessValidatation.IsSuccessful)
                return ActionResultResponse<NoResult, ResultId>(_mapper, userAccessValidatation);

            var response = await _productService.DeleteUOM(uomId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }
        #endregion

        #region  UOM Categories
        [HttpGet("UOMCaterory/Get")]
        public async Task<IActionResult> GetUOMCategoryList([FromQuery] Pagination pagination, [FromQuery] CommonFilterList commonFilter)
        {
            var response = await _productService.GetUOMCategoryList(pagination, commonFilter);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<UOMCategoryDto>, PagedList<UOMCategories>>(_mapper, response, pagination, PagedList<UOMCategories>.PagedMetadata(response));
        }

        [HttpGet("UOMCaterory/Get/{uomCategoryId}")]
        public async Task<IActionResult> GetUOMCategory(Guid uomCategoryId)
        {
            if (uomCategoryId == null || uomCategoryId == Guid.Empty)
            {
                return BadRequest(new { Message = "UOM Category ID is required." });
            }

            var response = await _productService.GetUOMCategoryByGuid(uomCategoryId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<UOMCategoryDto, UOMCategories>(_mapper, response);
        }
        #endregion

        #region Product Price
        [HttpGet("ProductPrice/Get")]
        public async Task<IActionResult> GetProductPriceList([FromQuery] Pagination pagination, [FromQuery] CommonFilterList commonFilter, [FromQuery] Guid? productUOMId)
        {
            var response = await _productService.GetProductPriceList(pagination, commonFilter, productUOMId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<ProductPriceDto>, PagedList<ProductPrice>>(_mapper, response, pagination, PagedList<ProductPrice>.PagedMetadata(response));
        }

        [HttpGet("ProductPrice/Get/{productPriceId}")]
        public async Task<IActionResult> GetProductPrice(Guid productPriceId)
        {
            if (productPriceId == null || productPriceId == Guid.Empty)
            {
                return BadRequest(new { Message = "Product Price ID is required." });
            }

            var response = await _productService.GetProductPriceByGuid(productPriceId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<ProductPriceDto, ProductPrice>(_mapper, response);
        }

        [HttpPost("ProductPrice/Create")]
        public async Task<IActionResult> CreateProductPrice([FromBody] ProductPriceRequest productPriceRequest)
        {
            var response = new BaseResponse<ResultId> { IsSuccessful = true };
            DolfinDbContext dbContext = null;

            try
            {
                // Create a database context for transaction
                dbContext = new DolfinDbContext(_dbContextOptions);
                var transaction = await dbContext.Database.BeginTransactionAsync();

                try
                {
                    // Validate ProductUOMId
                    var productUOMResponse = await _productService.GetProductUOMByGuid(productPriceRequest.ProductUOMId, dbContext);
                    if (!productUOMResponse.IsSuccessful || productUOMResponse.Result == null)
                    {
                        throw new ValidationException($"Product UOM with ID {productPriceRequest.ProductUOMId} not found.");
                    }

                    // If all validations pass, proceed with creating the product price
                    response = await _productService.InsertProductPrice(productPriceRequest, dbContext);
                    if (!response.IsSuccessful)
                    {
                        throw new Exception(response.Exception);
                    }

                    // Commit the transaction if everything succeeded
                    await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    // Rollback the transaction if any step failed
                    await transaction.RollbackAsync();
                    throw ex; // Re-throw to be caught by the outer catch block
                }
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            finally
            {
                // Dispose of the database context if it was created
                if (dbContext != null)
                {
                    await dbContext.DisposeAsync();
                }
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPut("ProductPrice/Update/{productPriceId}")]
        public async Task<IActionResult> UpdateProductPrice(Guid productPriceId, [FromBody] UpdateProductPriceRequest updateProductPriceRequest)
        {
            var response = new BaseResponse<ResultId> { IsSuccessful = true };
            DolfinDbContext dbContext = null;

            try
            {
                // Create a database context for transaction
                dbContext = new DolfinDbContext(_dbContextOptions);
                var transaction = await dbContext.Database.BeginTransactionAsync();

                try
                {
                    updateProductPriceRequest.Id = productPriceId;

                    // Get the existing ProductPrice to validate
                    var existingProductPrice = await _productService.GetProductPriceByGuid(productPriceId);
                    if (!existingProductPrice.IsSuccessful || existingProductPrice.Result == null)
                    {
                        throw new ValidationException($"Product Price with ID {productPriceId} not found.");
                    }

                    // If all validations pass, proceed with updating the product price
                    response = await _productService.UpdateProductPrice(updateProductPriceRequest, dbContext);
                    if (!response.IsSuccessful)
                    {
                        throw new Exception(response.Exception);
                    }

                    // Commit the transaction if everything succeeded
                    await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    // Rollback the transaction if any step failed
                    await transaction.RollbackAsync();
                    throw ex; // Re-throw to be caught by the outer catch block
                }
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            finally
            {
                // Dispose of the database context if it was created
                if (dbContext != null)
                {
                    await dbContext.DisposeAsync();
                }
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPost("ProductPrice/Delete")]
        public async Task<IActionResult> DeleteProductPrice(Guid productPriceId)
        {
            var userAccessValidatation = await _userService.UserAccessValidatation(true);
            if (!userAccessValidatation.IsSuccessful)
                return ActionResultResponse<NoResult, ResultId>(_mapper, userAccessValidatation);

            var response = await _productService.DeleteProductPrice(productPriceId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }
        #endregion
    }
}
