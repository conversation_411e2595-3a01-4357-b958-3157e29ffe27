﻿﻿using Dolfin.Framework.Data.Domains;

namespace Dolfin.Mobile.API.Models.Dto
{
    public class ProductPriceDto
    {
        public Guid Id { get; set; }
        public decimal FractionQty { get; set; }
        public decimal Price { get; set; }
        public DateTime EffectiveAt { get; set; }
        public string? Remark { get; set; }
        public Guid ProductUOMId { get; set; }
        //public virtual ProductUOMDto? ProductUOM { get; set; }
    }
}
