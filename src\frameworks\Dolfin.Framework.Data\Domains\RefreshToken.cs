using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Dolfin.Framework.Data.Domains.CustomIdentity;

namespace Dolfin.Framework.Data.Domains
{
    /// <summary>
    /// Represents a refresh token for JWT authentication
    /// </summary>
    public class RefreshToken
    {
        /// <summary>
        /// Unique identifier for the refresh token
        /// </summary>
        [Key]
        public string Token { get; set; }
        
        /// <summary>
        /// The JWT token ID this refresh token is associated with
        /// </summary>
        public string JwtId { get; set; }
        
        /// <summary>
        /// When the token was created
        /// </summary>
        public DateTime CreationDate { get; set; }
        
        /// <summary>
        /// When the token expires
        /// </summary>
        public DateTime ExpiryDate { get; set; }
        
        /// <summary>
        /// Has this token been used
        /// </summary>
        public bool Used { get; set; }
        
        /// <summary>
        /// Has this token been invalidated
        /// </summary>
        public bool Invalidated { get; set; }
        
        /// <summary>
        /// The user ID this token belongs to
        /// </summary>
        public string UserId { get; set; }
        
        /// <summary>
        /// The user this token belongs to
        /// </summary>
        [ForeignKey(nameof(UserId))]
        public virtual ApplicationUser User { get; set; }
    }
}
