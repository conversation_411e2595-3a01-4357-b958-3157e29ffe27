﻿using AutoMapper;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Infrastructure;
using Dolfin.Mobile.API.Models.Dto;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Reflection;
using ValidationException = Dolfin.Utility.Utils.ExceptionHandler.ValidationException;

namespace Dolfin.Mobile.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class CustomerController : ControllerCore
    {
        private readonly StandardMessage _standardMessage;
        private readonly ILogger<CustomerController> _logger;
        private readonly ICustomerService _customerService;
        private readonly IUserService _userService;
        private readonly IPrefixService _prefixService;
        private readonly IMapper _mapper;
        private readonly DbContextOptions<DolfinDbContext> _dbContextOptions;

        public CustomerController(
            ILogger<CustomerController> logger,
            ICustomerService customerService,
            IUserService userService,
            IPrefixService prefixService,
            IMapper mapper,
            DbContextOptions<DolfinDbContext> dbContextOptions)
        {
            _standardMessage = new StandardMessage();
            _logger = logger;
            _mapper = mapper;
            _customerService = customerService;
            _userService = userService;
            _prefixService = prefixService;
            _dbContextOptions = dbContextOptions;
        }

        #region  Customer
        [HttpGet("")]
        [RequirePermission(Permissions.Customer.View)]
        public async Task<IActionResult> GetCustomerList([FromQuery] Pagination pagination, [FromQuery] CommonFilterList commonFilter)
        {
            var response = await _customerService.GetCustomerList(pagination, commonFilter);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<CustomerDto>, PagedList<Customer>>(_mapper, response, pagination, PagedList<Customer>.PagedMetadata(response));
        }

        [HttpGet("Get/{customerId}")]
        [RequirePermission(Permissions.Customer.View)]
        public async Task<IActionResult> GetCustomerByGuid(Guid customerId)
        {
            if (customerId == null || customerId == Guid.Empty)
            {
                return BadRequest(new { Message = "Customer ID is required." });
            }

            var response = await _customerService.GetCustomerByGuid(customerId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<CustomerDto, Customer>(_mapper, response);
        }

        [HttpPost("Create")]
        [RequirePermission(Permissions.Customer.Create)]
        public async Task<IActionResult> CreateCustomer([FromBody] CustomerRequest customerRequest)
        {
            var response = new BaseResponse<ResultId> { IsSuccessful = true };
            DolfinDbContext dbContext = null;

            try
            {
                // Create a database context for transaction
                dbContext = new DolfinDbContext(_dbContextOptions);
                var transaction = await dbContext.Database.BeginTransactionAsync();

                try
                {

                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(customerRequest);
                    var currentUser = getCurrentUser.Item1;
                    customerRequest = getCurrentUser.Item2;
                    var companyId = currentUser.CompanyId;

                    // Check if phone number is provided and is unique
                    if (customerRequest.Address != null && !string.IsNullOrWhiteSpace(customerRequest.Address.PhoneNo))
                    {
                        // Normalize the phone number by removing spaces, dashes, etc.
                        string normalizedPhoneNo = new string(customerRequest.Address.PhoneNo.Where(c => char.IsDigit(c)).ToArray());
                        if (string.IsNullOrWhiteSpace(normalizedPhoneNo))
                        {
                            throw new ValidationException("Phone no incorrect format.");
                        }

                        var phoneExistsResponse = await _customerService.IsPhoneNumberExistsAsync(customerRequest.Address.PhoneNo, (Guid)companyId);
                        if (phoneExistsResponse)
                        {
                            throw new ValidationException($"A customer with phone number {customerRequest.Address.PhoneNo} already exists.");
                        }
                    }

                    // Generate a unique code for the customer using the prefix service
                    customerRequest.Code = await _prefixService.GenerateCode("Customer", customerRequest.Code, dbContext);

                    response = await _customerService.InsertCustomer(customerRequest, dbContext);
                    if (!response.IsSuccessful)
                    {
                        _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                    }
                }
                catch (Exception ex)
                {
                    // Rollback the transaction if any step failed
                    await transaction.RollbackAsync();
                    throw ex; // Re-throw to be caught by the outer catch block
                }
            }
            catch (ValidationException ex)
            {
                // Handle validation errors with BadRequest status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            catch (Exception ex)
            {
                // Handle other errors with InternalServerError status
                response = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    response,
                    Enums.StatusCode.InternalServerError,
                    exception: ex
                );
                _logger.LogError(ex, $"Error occurred in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            finally
            {
                // Dispose of the database context if it was created
                if (dbContext != null)
                {
                    await dbContext.DisposeAsync();
                }
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPut("Update/{customerId}")]
        [RequirePermission(Permissions.Customer.Update)]
        public async Task<IActionResult> UpdateCustomer(Guid customerId, [FromBody] UpdateCustomerRequest updateCustomerRequest)
        {
            var response = new BaseResponse<ResultId> { IsSuccessful = true };

            try
            {
                updateCustomerRequest.Id = customerId;

                // Get the existing customer to check if phone number is being changed
                var existingCustomer = await _customerService.GetCustomerByGuid(customerId);
                if (!existingCustomer.IsSuccessful || existingCustomer.Result == null)
                {
                    return NotFound(new { Message = $"Customer with ID {customerId} not found." });
                }

                response = await _customerService.UpdateCustomer(updateCustomerRequest);
                if (!response.IsSuccessful)
                {
                    _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error updating customer in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                response.IsSuccessful = false;
                response.Exception = ex.Message;
                return StatusCode(500, new { Message = "An error occurred while processing your request." });
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, response);
        }

        [HttpPost("Delete")]
        [RequirePermission(Permissions.Customer.Delete)]
        public async Task<IActionResult> DeleteCustomer(Guid customerId)
        {
            var userAccessValidatation = await _userService.UserAccessValidatation(true);
            if (!userAccessValidatation.IsSuccessful)
                return ActionResultResponse<NoResult, ResultId>(_mapper, userAccessValidatation);

            var response = await _customerService.DeleteCustomer(customerId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }
        #endregion

        #region Term
        [HttpGet("Terms")]
        [RequirePermission(Permissions.Customer.View)]
        public async Task<IActionResult> GetTermList([FromQuery] Pagination pagination)
        {
            var response = await _customerService.GetTermList(pagination);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<TermDto>, PagedList<Term>>(_mapper, response, pagination, PagedList<Term>.PagedMetadata(response));
        }

        [HttpGet("Term/{termId}")]
        [RequirePermission(Permissions.Customer.View)]
        public async Task<IActionResult> GetTermByGuid(Guid termId)
        {
            if (termId == null || termId == Guid.Empty)
            {
                return BadRequest(new { Message = "Term ID is required." });
            }

            var response = await _customerService.GetTermByGuid(termId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<TermDto, Term>(_mapper, response);
        }
        #endregion

        #region Address
        [HttpGet("Address/{addressId}")]
        [RequirePermission(Permissions.Customer.View)]
        public async Task<IActionResult> GetAddressByGuid(Guid addressId)
        {
            if (addressId == null || addressId == Guid.Empty)
            {
                return BadRequest(new { Message = "Address ID is required." });
            }

            var response = await _customerService.GetAddressByGuid(addressId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<AddressDto, Address>(_mapper, response);
        }
        #endregion

    }
}
