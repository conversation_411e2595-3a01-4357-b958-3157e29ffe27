using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using NUnit.Framework;
using System;
using Dolfin.Framework.Repository.Common;
using Dolfin.Framework.Repository.Implementations;

namespace Dolfin.Mobile.API.Tests
{
    /// <summary>
    /// Tests for cache clearing functionality
    /// </summary>
    [TestFixture]
    public class CacheClearingTests
    {
        private CacheService _cacheService;
        private IMemoryCache _memoryCache;
        private Mock<ILogger<CacheService>> _mockLogger;
        private CacheSettings _cacheSettings;

        [SetUp]
        public void Setup()
        {
            _memoryCache = new MemoryCache(new MemoryCacheOptions());
            _mockLogger = new Mock<ILogger<CacheService>>();
            _cacheSettings = new CacheSettings
            {
                DefaultAbsoluteExpirationMinutes = 60,
                DefaultSlidingExpirationMinutes = 30,
                UserCacheExpirationMinutes = 60
            };

            var options = Options.Create(_cacheSettings);
            _cacheService = new CacheService(_memoryCache, options, _mockLogger.Object);
        }

        [TearDown]
        public void TearDown()
        {
            _memoryCache?.Dispose();
        }

        [Test]
        public void ClearUserCache_WithValidUserId_ShouldRemoveUserSpecificEntries()
        {
            // Arrange
            var userId = "test-user-123";
            var userCacheKey = $"CurrentUser_{userId}";
            
            // Add some cache entries
            _memoryCache.Set(userCacheKey, "user data");
            _memoryCache.Set("SomeOtherKey", "other data");

            // Verify entries exist
            Assert.IsTrue(_memoryCache.TryGetValue(userCacheKey, out _));
            Assert.IsTrue(_memoryCache.TryGetValue("SomeOtherKey", out _));

            // Act
            _cacheService.ClearUserCache(userId);

            // Assert
            Assert.IsFalse(_memoryCache.TryGetValue(userCacheKey, out _));
            Assert.IsTrue(_memoryCache.TryGetValue("SomeOtherKey", out _)); // Other entries should remain
        }

        [Test]
        public void ClearCompanyCache_WithValidCompanyId_ShouldRemoveCompanySpecificEntries()
        {
            // Arrange
            var companyId = Guid.NewGuid();
            var companyCacheKey = $"Company_{companyId}";
            var productListKey = $"ProductList_Company_{companyId}";
            
            // Add some cache entries
            _memoryCache.Set(companyCacheKey, "company data");
            _memoryCache.Set(productListKey, "product list");
            _memoryCache.Set("SomeOtherKey", "other data");

            // Verify entries exist
            Assert.IsTrue(_memoryCache.TryGetValue(companyCacheKey, out _));
            Assert.IsTrue(_memoryCache.TryGetValue(productListKey, out _));
            Assert.IsTrue(_memoryCache.TryGetValue("SomeOtherKey", out _));

            // Act
            _cacheService.ClearCompanyCache(companyId);

            // Assert
            Assert.IsFalse(_memoryCache.TryGetValue(companyCacheKey, out _));
            Assert.IsFalse(_memoryCache.TryGetValue(productListKey, out _));
            Assert.IsTrue(_memoryCache.TryGetValue("SomeOtherKey", out _)); // Other entries should remain
        }

        [Test]
        public void ClearAll_ShouldAttemptToClearAllEntries()
        {
            // Arrange
            _memoryCache.Set("Key1", "Value1");
            _memoryCache.Set("Key2", "Value2");
            _memoryCache.Set("Key3", "Value3");

            // Verify entries exist
            Assert.IsTrue(_memoryCache.TryGetValue("Key1", out _));
            Assert.IsTrue(_memoryCache.TryGetValue("Key2", out _));
            Assert.IsTrue(_memoryCache.TryGetValue("Key3", out _));

            // Act
            _cacheService.ClearAll();

            // Assert - Note: Due to reflection-based clearing, we mainly verify no exceptions are thrown
            // The actual clearing success depends on the .NET runtime version and internal implementation
            Assert.DoesNotThrow(() => _cacheService.ClearAll());
        }

        [Test]
        public void ClearUserCache_WithNullUserId_ShouldLogWarningAndNotThrow()
        {
            // Act & Assert
            Assert.DoesNotThrow(() => _cacheService.ClearUserCache(null));
            Assert.DoesNotThrow(() => _cacheService.ClearUserCache(""));
            Assert.DoesNotThrow(() => _cacheService.ClearUserCache("   "));
        }

        [Test]
        public void GetOrCreate_ShouldCacheAndRetrieveValues()
        {
            // Arrange
            var key = "test-key";
            var value = "test-value";
            var factoryCallCount = 0;

            // Act - First call should invoke factory
            var result1 = _cacheService.GetOrCreate(key, () =>
            {
                factoryCallCount++;
                return value;
            });

            // Act - Second call should return cached value
            var result2 = _cacheService.GetOrCreate(key, () =>
            {
                factoryCallCount++;
                return "different-value";
            });

            // Assert
            Assert.AreEqual(value, result1);
            Assert.AreEqual(value, result2);
            Assert.AreEqual(1, factoryCallCount); // Factory should only be called once
        }
    }
}
