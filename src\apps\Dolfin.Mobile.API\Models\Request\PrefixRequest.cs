using System.ComponentModel.DataAnnotations;

namespace Dolfin.Mobile.API.Models.Request
{
    /// <summary>
    /// Request model for creating a new prefix
    /// </summary>
    public class PrefixRequest
    {
        /// <summary>
        /// The name of the table for which the prefix is being created
        /// </summary>
        [Required]
        public required string TableName { get; set; }

        /// <summary>
        /// The prefix value to use (e.g., "CUST" for customers)
        /// </summary>
        [Required]
        public required string PrefixValue { get; set; }

        /// <summary>
        /// The padding length for the numeric part (default: 5)
        /// </summary>
        public int PaddingLength { get; set; } = 5;

        /// <summary>
        /// The user ID to associate with this prefix (optional)
        /// </summary>
        public string? UserId { get; set; }
        
        /// <summary>
        /// The branch ID to associate with this prefix
        /// </summary>
        public Guid? BranchId { get; set; }
    }
}
