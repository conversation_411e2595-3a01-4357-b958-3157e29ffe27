﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public class TransactionItemStatus : _BaseDomain
    {
        public TransactionItemStatus()
        {
            TransactionItem = new HashSet<TransactionItem>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public virtual ICollection<TransactionItem> TransactionItem { get; }
    }
}
