﻿using AutoMapper;
using DocumentFormat.OpenXml.Spreadsheet;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Infrastructure;
using Dolfin.Mobile.API.Models.Dto;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Reflection;
using ValidationException = Dolfin.Utility.Utils.ExceptionHandler.ValidationException;

namespace Dolfin.Mobile.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class TransactionController : ControllerCore
    {
        private StandardMessage _standardMessage;
        private readonly ILogger<TransactionController> _logger;
        private readonly ITransactionService _transactionService;
        private readonly IUserService _userService;
        private readonly ICompanyService _companyService;
        private readonly IProductService _productService;
        private readonly ICustomerService _customerService;
        private readonly IPrefixService _prefixService;
        private IMapper _mapper;
        public TransactionController(ILogger<TransactionController> logger, ITransactionService transactionService, IUserService userService, ICompanyService companyService, IProductService productService, ICustomerService customerService, IPrefixService prefixService, IMapper mapper)
        {
            _standardMessage = new StandardMessage();
            _logger = logger;
            _mapper = mapper;
            _transactionService = transactionService;
            _companyService = companyService;
            _productService = productService;
            _customerService = customerService;
            _userService = userService;
            _prefixService = prefixService;
        }

        #region  Transaction
        [HttpGet("Transaction")]
        public async Task<IActionResult> GetTransactionList([FromQuery] Pagination pagination, [FromQuery] CommonFilterList commonFilter)
        {
            var response = await _transactionService.GetTransactionList(pagination, commonFilter);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<TransactionDto>, PagedList<Transaction>>(_mapper, response, pagination, PagedList<Transaction>.PagedMetadata(response));
        }

        [HttpGet("Transaction/Get")]
        [RequirePermission(Permissions.Transaction.View)]
        public async Task<IActionResult> GetTransaction(Guid TransactionId)
        {
            if (TransactionId == null || TransactionId == Guid.Empty)
            {
                return BadRequest(new { Message = "Transaction ID is required." });
            }

            var response = await _transactionService.GetTransactionByGuid(TransactionId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<TransactionDto, Transaction>(_mapper, response);
        }

        [HttpPost("Transaction/Create")]
        [RequirePermission(Permissions.Transaction.Create)]
        public async Task<IActionResult> CreateTransaction([FromBody] TransactionRequest reqBody, bool isCalculationRequired)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            var dbContext = _transactionService.CreateDbContext();
            var transaction = dbContext.Database.BeginTransaction();
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithBranchAsync(reqBody);
                var currentUser = getCurrentUser.Item1;
                reqBody = getCurrentUser.Item2;

                if (reqBody.TransactionItemRequest.FindAll(x => x.SalesTaxNoId.HasValue && x.SalesTaxNoId != Guid.Empty && x.ServiceTaxNoId.HasValue && x.ServiceTaxNoId != Guid.Empty).Count > 0)
                {
                    throw new ValidationException("Transaction Item Sales Tax ID and Service Tax Id only one is required.");
                }

                // Validate required foreign keys
                // Validate CustomerId
                var customerResponse = await _customerService.GetCustomerByGuid(reqBody.CustomerId);
                if (!customerResponse.IsSuccessful || customerResponse.Result == null)
                {
                    throw new ValidationException($"Customer with ID {reqBody.CustomerId} not found.");
                }

                // Validate BranchId
                //var branchResponse = await _companyService.GetBranchByGuid(reqBody.BranchId);
                //if (!branchResponse.IsSuccessful || branchResponse.Result == null)
                //{
                //    throw new ValidationException($"Branch with ID {reqBody.BranchId} not found.");
                //}

                // Validate TransactionTypeId
                var transactionTypeResponse = await _transactionService.GetTransactionTypeByGuid(reqBody.TransactionTypeId);
                if (!transactionTypeResponse.IsSuccessful || transactionTypeResponse.Result == null)
                {
                    throw new ValidationException($"Transaction Type with ID {reqBody.TransactionTypeId} not found.");
                }

                // Validate TransactionStatusId
                var transactionStatusResponse = await _transactionService.GetTransactionStatusByGuid(reqBody.TransactionStatusId);
                if (!transactionStatusResponse.IsSuccessful || transactionStatusResponse.Result == null)
                {
                    throw new ValidationException($"Transaction Status with ID {reqBody.TransactionStatusId} not found.");
                }

                // Validate ShippingAddressId
                var shippingAddressResponse = await _customerService.GetAddressByGuid(reqBody.ShippingAddressId);
                if (!shippingAddressResponse.IsSuccessful || shippingAddressResponse.Result == null)
                {
                    throw new ValidationException($"Shipping Address with ID {reqBody.ShippingAddressId} not found.");
                }

                // Validate BillingAddressId
                var billingAddressResponse = await _customerService.GetAddressByGuid(reqBody.BillingAddressId);
                if (!billingAddressResponse.IsSuccessful || billingAddressResponse.Result == null)
                {
                    throw new ValidationException($"Billing Address with ID {reqBody.BillingAddressId} not found.");
                }

                // Validate optional foreign keys
                //// Validate SalesTaxNoId if provided
                //if (reqBody.SalesTaxNoId.HasValue && reqBody.SalesTaxNoId != Guid.Empty)
                //{
                //    var salesTaxResponse = await _companyService.GetTaxRateByGuid(reqBody.SalesTaxNoId.Value);
                //    if (!salesTaxResponse.IsSuccessful || salesTaxResponse.Result == null)
                //    {
                //        throw new ValidationException($"Sales Tax Rate with ID {reqBody.SalesTaxNoId.Value} not found.");
                //    }
                //}

                //// Validate ServiceTaxNoId if provided
                //if (reqBody.ServiceTaxNoId.HasValue && reqBody.ServiceTaxNoId != Guid.Empty)
                //{
                //    var serviceTaxResponse = await _companyService.GetTaxRateByGuid(reqBody.ServiceTaxNoId.Value);
                //    if (!serviceTaxResponse.IsSuccessful || serviceTaxResponse.Result == null)
                //    {
                //        throw new ValidationException($"Service Tax Rate with ID {reqBody.ServiceTaxNoId.Value} not found.");
                //    }
                //}

                reqBody.SalesTaxNoId = currentUser.Company.DefaultSalesTaxNoId;
                reqBody.ServiceTaxNoId = currentUser.Company.DefaultServiceTaxNoId;

                if (reqBody.SalesTaxNoId.HasValue && reqBody.SalesTaxNoId != Guid.Empty && reqBody.ServiceTaxNoId.HasValue && reqBody.ServiceTaxNoId != Guid.Empty)
                {
                    throw new ValidationException("Sales Tax ID and Service Tax Id only one is required.");
                }
                else if (reqBody.SalesTaxNoId.HasValue && reqBody.SalesTaxNoId != Guid.Empty)
                {
                    reqBody.SalesTaxRate = decimal.Parse(currentUser.Company.DefaultSalesTaxNo.ChargePercentage.ToString());
                }
                else if (reqBody.ServiceTaxNoId.HasValue && reqBody.ServiceTaxNoId != Guid.Empty)
                {
                    reqBody.ServiceTaxRate = decimal.Parse(currentUser.Company.DefaultServiceTaxNo.ChargePercentage.ToString());
                }

                // Validate TermId if provided
                if (reqBody.TermId.HasValue && reqBody.TermId != Guid.Empty)
                {
                    var termResponse = await _customerService.GetTermByGuid(reqBody.TermId.Value);
                    if (!termResponse.IsSuccessful || termResponse.Result == null)
                    {
                        throw new ValidationException($"Term with ID {reqBody.TermId.Value} not found.");
                    }
                }

                // Validate AccountGroupId if provided
                if (reqBody.AccountGroupId.HasValue && reqBody.AccountGroupId != Guid.Empty)
                {
                    var accountGroupResponse = await _transactionService.GetAccountGroupByGuid(reqBody.AccountGroupId.Value);
                    if (!accountGroupResponse.IsSuccessful || accountGroupResponse.Result == null)
                    {
                        throw new ValidationException($"Account Group with ID {reqBody.AccountGroupId.Value} not found.");
                    }
                }

                // Validate TrxNo uniqueness within the branch
                var existingTransaction = await dbContext.Set<Transaction>()
                    .Where(t => t.TrxNo == reqBody.TrxNo && t.BranchId == reqBody.BranchId && t.IsActive)
                    .FirstOrDefaultAsync();

                if (existingTransaction != null)
                {
                    throw new ValidationException($"Transaction number '{reqBody.TrxNo}' already exists in this branch. Transaction numbers must be unique within each branch.");
                }

                // Validate transaction items
                foreach (var transactionItemRequest in reqBody.TransactionItemRequest)
                {
                    // Validate ProductId
                    var productResponse = await _productService.GetProductByGuid(transactionItemRequest.ProductId);
                    if (!productResponse.IsSuccessful || productResponse.Result == null)
                    {
                        throw new ValidationException($"Product with ID {transactionItemRequest.ProductId} not found.");
                    }

                    // Get ClassificationCode and ClassificationId from Product's Classification
                    if (productResponse.Result.Classification == null || string.IsNullOrEmpty(productResponse.Result?.Classification?.Code))
                    {
                        throw new ValidationException($"Product with ID {transactionItemRequest.ProductId} does not have a valid Classification Code.");
                    }
                    transactionItemRequest.ClassificationCode = productResponse.Result.Classification.Code;
                    transactionItemRequest.ClassificationId = productResponse.Result.Classification.Id;

                    // Validate ProductUOMId and related logic
                    if (transactionItemRequest.ProductUOMId == null || transactionItemRequest.ProductUOMId == Guid.Empty)
                    {
                        // Check if ProductUOMPrimaryMCode and ProductUOMSecondaryMCode are required
                        if (string.IsNullOrEmpty(transactionItemRequest.ProductUOMPrimaryMCode))
                        {
                            throw new ValidationException($"ProductUOMPrimaryMCode is required when ProductUOMId is not provided for Product {transactionItemRequest.ProductId}.");
                        }
                        if (string.IsNullOrEmpty(transactionItemRequest.ProductUOMSecondaryMCode))
                        {
                            throw new ValidationException($"ProductUOMSecondaryMCode is required when ProductUOMId is not provided for Product {transactionItemRequest.ProductId}.");
                        }
                    }
                    else
                    {
                        // Validate ProductUOMId exists
                        var productUOMResponse = await _productService.GetProductUOMByGuid(transactionItemRequest.ProductUOMId, dbContext);
                        if (!productUOMResponse.IsSuccessful || productUOMResponse.Result == null)
                        {
                            throw new ValidationException($"ProductUOM with ID {transactionItemRequest.ProductUOMId} not found.");
                        }

                        // Validate that ProductUOMId's ProductId matches the passed ProductId
                        if (productUOMResponse.Result.ProductId != transactionItemRequest.ProductId)
                        {
                            throw new ValidationException($"ProductUOM with ID {transactionItemRequest.ProductUOMId} does not belong to Product {transactionItemRequest.ProductId}.");
                        }

                        // Assign ProductUOMId's FractionTotal to request body FractionTotal
                        transactionItemRequest.FractionTotal = productUOMResponse.Result.FractionTotal;

                        // Assign ProductUOMId's product price fractionQty to request body FractionQuantity
                        if (productUOMResponse.Result.EffectivedProductPrice != null)
                        {
                            transactionItemRequest.FractionQuantity = productUOMResponse.Result.EffectivedProductPrice.FractionQty;
                        }

                        // Set ProductUOMPrimaryMCode and ProductUOMSecondaryMCode from ProductUOM
                        if (productUOMResponse.Result.UomPrimary != null)
                        {
                            transactionItemRequest.ProductUOMPrimaryMCode = productUOMResponse.Result.UomPrimary.Code;
                        }

                        // Validate that ProductUOMPrimaryMCode is not null or empty after assignment
                        if (string.IsNullOrEmpty(transactionItemRequest.ProductUOMPrimaryMCode))
                        {
                            throw new ValidationException($"ProductUOM with ID {transactionItemRequest.ProductUOMId} does not have a valid Primary UOM Code.");
                        }

                        if (productUOMResponse.Result?.UomSecondary != null)
                        {
                            transactionItemRequest.ProductUOMSecondaryMCode = productUOMResponse.Result.UomSecondary.Code;
                        }

                        // Validate that EffectivedProductPrice exists before price validation
                        if (productUOMResponse.Result?.EffectivedProductPrice == null)
                        {
                            throw new ValidationException($"ProductUOM with ID {transactionItemRequest.ProductUOMId} does not have an effective product price.");
                        }

                        // Validate that payload ProductPriceId matches ProductUOM's EffectivedProductPrice Id
                        if (transactionItemRequest.ProductPriceId != productUOMResponse.Result.EffectivedProductPrice.Id)
                        {
                            throw new ValidationException($"ProductPriceId {transactionItemRequest.ProductPriceId} does not match the effective product price for ProductUOM {transactionItemRequest.ProductUOMId}.");
                        }

                        // Validate quantity is within OrderMinQty and OrderMaxQty range
                        if (productUOMResponse.Result.OrderMinQty.HasValue && transactionItemRequest.Quantity < productUOMResponse.Result.OrderMinQty.Value)
                        {
                            throw new ValidationException($"Quantity {transactionItemRequest.Quantity} is below the minimum order quantity {productUOMResponse.Result.OrderMinQty.Value} for ProductUOM {transactionItemRequest.ProductUOMId}.");
                        }

                        if (productUOMResponse.Result.OrderMaxQty.HasValue && transactionItemRequest.Quantity > productUOMResponse.Result.OrderMaxQty.Value)
                        {
                            throw new ValidationException($"Quantity {transactionItemRequest.Quantity} is above the maximum order quantity {productUOMResponse.Result.OrderMaxQty.Value} for ProductUOM {transactionItemRequest.ProductUOMId}.");
                        }

                        // Validate price based on PriceEditable setting
                        if (!productUOMResponse.Result.PriceEditable)
                        {
                            // If PriceEditable is false, check if UnitAmount matches EffectivedProductPrice's price
                            if (transactionItemRequest.UnitAmount != productUOMResponse.Result.EffectivedProductPrice.Price)
                            {
                                throw new ValidationException($"UnitAmount {transactionItemRequest.UnitAmount} does not match the fixed price {productUOMResponse.Result.EffectivedProductPrice.Price} for ProductUOM {transactionItemRequest.ProductUOMId}.");
                            }
                        }
                        else
                        {
                            // If PriceEditable is true, check if UnitAmount is within MinEditPrice and MaxEditPrice range
                            if (productUOMResponse.Result.MinEditPrice.HasValue && transactionItemRequest.UnitAmount < productUOMResponse.Result.MinEditPrice.Value)
                            {
                                throw new ValidationException($"UnitAmount {transactionItemRequest.UnitAmount} is below the minimum allowed price {productUOMResponse.Result.MinEditPrice.Value} for ProductUOM {transactionItemRequest.ProductUOMId}.");
                            }

                            if (productUOMResponse.Result.MaxEditPrice.HasValue && transactionItemRequest.UnitAmount > productUOMResponse.Result.MaxEditPrice.Value)
                            {
                                throw new ValidationException($"UnitAmount {transactionItemRequest.UnitAmount} is above the maximum allowed price {productUOMResponse.Result.MaxEditPrice.Value} for ProductUOM {transactionItemRequest.ProductUOMId}.");
                            }
                        }
                    }
                }

                if (isCalculationRequired)
                {
                    int? serviceCharge = currentUser?.Company?.ServiceCharge != null ? int.Parse(currentUser.Company.ServiceCharge.ToString()) : null;

                    var transactionCalculate = new TransactionCalculate();
                    foreach (var transactionItemRequest in reqBody.TransactionItemRequest)
                    {
                        transactionItemRequest.OrderGroup = 1;

                        var calculationTransactionItem = await CalculationTransactionItem(transactionItemRequest, (Guid)currentUser.BranchId);
                        _mapper.Map(transactionItemRequest, transactionItemRequest);

                        transactionCalculate.SalesTaxAmount += calculationTransactionItem.SalesTaxAmount;
                        transactionCalculate.ServiceTaxAmount += calculationTransactionItem.ServiceTaxAmount;
                        transactionCalculate.TaxExclAmount += calculationTransactionItem.TaxExclAmount;
                        transactionCalculate.TaxInclAmount += calculationTransactionItem.TaxInclAmount;
                        transactionCalculate.TotalUnitAmountWOTax += calculationTransactionItem.TotalUnitAmountWOTax;
                        transactionCalculate.TotalUnitAmount += calculationTransactionItem.TotalUnitAmount;
                        transactionCalculate.SubTotalAmount += calculationTransactionItem.SubTotalAmount;
                    }

                    reqBody.TotalExclTax = transactionCalculate.TaxExclAmount;
                    reqBody.TotalInclTax = transactionCalculate.TaxInclAmount;
                    reqBody.TotalTaxAmount = transactionCalculate.SubTotalAmount;
                    reqBody.TotalSalesTaxAmount = transactionCalculate.SalesTaxAmount != 0.0m ? transactionCalculate.SalesTaxAmount : null;
                    reqBody.TotalServiceTaxAmount = transactionCalculate.ServiceTaxAmount != 0.0m ? transactionCalculate.ServiceTaxAmount : null;
                    reqBody.TotalServiceChargePerc = serviceCharge;
                    reqBody.TotalServiceChargeAmount = serviceCharge != null ? Math.Round(transactionCalculate.TotalUnitAmountWOTax * decimal.Parse(serviceCharge.ToString()) / 100m, 2, MidpointRounding.AwayFromZero) : 0.00m;

                    (decimal grandTotalAmount, decimal grandTotalAdjustmentAmount) = SharedFunctionHelper.CalculateRoundingAdjustment(transactionCalculate.SubTotalAmount);
                    reqBody.TotalAmount = grandTotalAmount;
                    reqBody.TotalRoundingAdjustmentAmount = grandTotalAdjustmentAmount;
                }
                reqBody.UserId = currentUser.Id;

                // Generate a unique code for the customer using the prefix service
                reqBody.TrxNo = await _prefixService.GenerateCode("Customer", reqBody.TrxNo, dbContext);

                result = await _transactionService.InsertTransaction(reqBody, dbContext);
                if (!result.IsSuccessful)
                {
                    _logger.LogError(result.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                    throw new Exception(result.Exception);
                }

                var trxId = result.Result.Id;
                int orderGroup = 1;
                foreach (var transactionItemRequest in reqBody.TransactionItemRequest)
                {
                    transactionItemRequest.TrxId = trxId;
                    transactionItemRequest.OrderGroup = orderGroup;
                    var resultItem = await _transactionService.InsertTransactionItem(transactionItemRequest, dbContext);
                    if (!resultItem.IsSuccessful)
                    {
                        _logger.LogError(result.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                        throw new Exception(transactionItemRequest.ProductId + " " + resultItem.Exception);
                    }
                }
                await transaction.CommitAsync();
            }
            catch (ValidationException ex)
            {
                await transaction.RollbackAsync();
                // Handle validation errors with BadRequest status
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    result,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}: {ex.Message}");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                await transaction.DisposeAsync();
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, result);
        }

        [HttpPost("Transaction/Update")]
        [RequirePermission(Permissions.Transaction.Update)]
        public async Task<IActionResult> UpdateTransaction([FromBody] UpdateTransactionRequest reqBody, bool isCalculationRequired)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            var dbContext = _transactionService.CreateDbContext();
            var transaction = dbContext.Database.BeginTransaction();
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithBranchAsync(reqBody);
                var currentUser = getCurrentUser.Item1;
                reqBody = getCurrentUser.Item2;

                // Get existing transaction with transaction items
                var existingTransactionResponse = await _transactionService.GetTransactionByGuid(reqBody.Id);
                if (!existingTransactionResponse.IsSuccessful || existingTransactionResponse.Result == null)
                {
                    throw new ValidationException($"Transaction with ID {reqBody.Id} not found.");
                }

                // Validate required foreign keys
                // Validate CustomerId
                var customerResponse = await _customerService.GetCustomerByGuid(reqBody.CustomerId);
                if (!customerResponse.IsSuccessful || customerResponse.Result == null)
                {
                    throw new ValidationException($"Customer with ID {reqBody.CustomerId} not found.");
                }

                // Validate TransactionTypeId
                var transactionTypeResponse = await _transactionService.GetTransactionTypeByGuid(reqBody.TransactionTypeId);
                if (!transactionTypeResponse.IsSuccessful || transactionTypeResponse.Result == null)
                {
                    throw new ValidationException($"Transaction Type with ID {reqBody.TransactionTypeId} not found.");
                }

                // Validate TransactionStatusId
                var transactionStatusResponse = await _transactionService.GetTransactionStatusByGuid(reqBody.TransactionStatusId);
                if (!transactionStatusResponse.IsSuccessful || transactionStatusResponse.Result == null)
                {
                    throw new ValidationException($"Transaction Status with ID {reqBody.TransactionStatusId} not found.");
                }

                // Validate ShippingAddressId
                var shippingAddressResponse = await _customerService.GetAddressByGuid(reqBody.ShippingAddressId);
                if (!shippingAddressResponse.IsSuccessful || shippingAddressResponse.Result == null)
                {
                    throw new ValidationException($"Shipping Address with ID {reqBody.ShippingAddressId} not found.");
                }

                // Validate BillingAddressId
                var billingAddressResponse = await _customerService.GetAddressByGuid(reqBody.BillingAddressId);
                if (!billingAddressResponse.IsSuccessful || billingAddressResponse.Result == null)
                {
                    throw new ValidationException($"Billing Address with ID {reqBody.BillingAddressId} not found.");
                }

                // Validate optional foreign keys
                // Validate SalesTaxNoId if provided
                //if (reqBody.SalesTaxNoId.HasValue && reqBody.SalesTaxNoId != Guid.Empty)
                //{
                //    var salesTaxResponse = await _companyService.GetTaxRateByGuid((Guid)reqBody.SalesTaxNoId);
                //    if (!salesTaxResponse.IsSuccessful || salesTaxResponse.Result == null)
                //    {
                //        throw new ValidationException($"Sales Tax Rate with ID {reqBody.SalesTaxNoId} not found.");
                //    }
                //}

                //// Validate ServiceTaxNoId if provided
                //if (reqBody.ServiceTaxNoId.HasValue && reqBody.ServiceTaxNoId != Guid.Empty)
                //{
                //    var serviceTaxResponse = await _companyService.GetTaxRateByGuid((Guid)reqBody.ServiceTaxNoId);
                //    if (!serviceTaxResponse.IsSuccessful || serviceTaxResponse.Result == null)
                //    {
                //        throw new ValidationException($"Service Tax Rate with ID {reqBody.ServiceTaxNoId} not found.");
                //    }
                //}

                reqBody.SalesTaxNoId = currentUser.Company.DefaultSalesTaxNoId;
                reqBody.ServiceTaxNoId = currentUser.Company.DefaultServiceTaxNoId;

                if (reqBody.SalesTaxNoId.HasValue && reqBody.SalesTaxNoId != Guid.Empty && reqBody.ServiceTaxNoId.HasValue && reqBody.ServiceTaxNoId != Guid.Empty)
                {
                    throw new ValidationException("Sales Tax ID and Service Tax Id only one is required.");
                }
                else if (reqBody.SalesTaxNoId.HasValue && reqBody.SalesTaxNoId != Guid.Empty)
                {
                    reqBody.SalesTaxRate = decimal.Parse(currentUser.Company.DefaultSalesTaxNo.ChargePercentage.ToString());
                }
                else if (reqBody.ServiceTaxNoId.HasValue && reqBody.ServiceTaxNoId != Guid.Empty)
                {
                    reqBody.ServiceTaxRate = decimal.Parse(currentUser.Company.DefaultServiceTaxNo.ChargePercentage.ToString());
                }

                // Validate TermId if provided
                if (reqBody.TermId.HasValue && reqBody.TermId != Guid.Empty)
                {
                    var termResponse = await _customerService.GetTermByGuid(reqBody.TermId.Value);
                    if (!termResponse.IsSuccessful || termResponse.Result == null)
                    {
                        throw new ValidationException($"Term with ID {reqBody.TermId.Value} not found.");
                    }
                }

                // Validate AccountGroupId if provided
                if (reqBody.AccountGroupId.HasValue && reqBody.AccountGroupId != Guid.Empty)
                {
                    var accountGroupResponse = await _transactionService.GetAccountGroupByGuid(reqBody.AccountGroupId.Value);
                    if (!accountGroupResponse.IsSuccessful || accountGroupResponse.Result == null)
                    {
                        throw new ValidationException($"Account Group with ID {reqBody.AccountGroupId.Value} not found.");
                    }
                }

                // Note: TrxNo cannot be updated in UpdateTransaction, so no need to validate uniqueness
                // The existing transaction already has a valid unique TrxNo

                // Validate transaction items
                foreach (var transactionItemRequest in reqBody.TransactionItemRequest)
                {
                    // Validate ProductId
                    var productResponse = await _productService.GetProductByGuid(transactionItemRequest.ProductId);
                    if (!productResponse.IsSuccessful || productResponse.Result == null)
                    {
                        throw new ValidationException($"Product with ID {transactionItemRequest.ProductId} not found.");
                    }

                    // Get ClassificationCode and ClassificationId from Product's Classification
                    if (productResponse.Result.Classification == null || string.IsNullOrEmpty(productResponse.Result?.Classification?.Code))
                    {
                        throw new ValidationException($"Product with ID {transactionItemRequest.ProductId} does not have a valid Classification Code.");
                    }
                    transactionItemRequest.ClassificationCode = productResponse.Result.Classification.Code;
                    transactionItemRequest.ClassificationId = productResponse.Result.Classification.Id;

                    // Validate ProductUOMId and related logic
                    if (transactionItemRequest.ProductUOMId == null || transactionItemRequest.ProductUOMId == Guid.Empty)
                    {
                        // Check if ProductUOMPrimaryMCode and ProductUOMSecondaryMCode are required
                        if (string.IsNullOrEmpty(transactionItemRequest.ProductUOMPrimaryMCode))
                        {
                            throw new ValidationException($"ProductUOMPrimaryMCode is required when ProductUOMId is not provided for Product {transactionItemRequest.ProductId}.");
                        }
                        if (string.IsNullOrEmpty(transactionItemRequest.ProductUOMSecondaryMCode))
                        {
                            throw new ValidationException($"ProductUOMSecondaryMCode is required when ProductUOMId is not provided for Product {transactionItemRequest.ProductId}.");
                        }
                    }
                    else
                    {
                        // Validate ProductUOMId exists
                        var productUOMResponse = await _productService.GetProductUOMByGuid(transactionItemRequest.ProductUOMId, dbContext);
                        if (!productUOMResponse.IsSuccessful || productUOMResponse.Result == null)
                        {
                            throw new ValidationException($"ProductUOM with ID {transactionItemRequest.ProductUOMId} not found.");
                        }

                        // Validate that ProductUOMId's ProductId matches the passed ProductId
                        if (productUOMResponse.Result.ProductId != transactionItemRequest.ProductId)
                        {
                            throw new ValidationException($"ProductUOM with ID {transactionItemRequest.ProductUOMId} does not belong to Product {transactionItemRequest.ProductId}.");
                        }

                        // Assign ProductUOMId's FractionTotal to request body FractionTotal
                        transactionItemRequest.FractionTotal = productUOMResponse.Result.FractionTotal;

                        // Assign ProductUOMId's product price fractionQty to request body FractionQuantity
                        if (productUOMResponse.Result.EffectivedProductPrice != null)
                        {
                            transactionItemRequest.FractionQuantity = productUOMResponse.Result.EffectivedProductPrice.FractionQty;
                        }

                        // Set ProductUOMPrimaryMCode and ProductUOMSecondaryMCode from ProductUOM
                        if (productUOMResponse.Result.UomPrimary != null)
                        {
                            transactionItemRequest.ProductUOMPrimaryMCode = productUOMResponse.Result.UomPrimary.Code;
                        }

                        // Validate that ProductUOMPrimaryMCode is not null or empty after assignment
                        if (string.IsNullOrEmpty(transactionItemRequest.ProductUOMPrimaryMCode))
                        {
                            throw new ValidationException($"ProductUOM with ID {transactionItemRequest.ProductUOMId} does not have a valid Primary UOM Code.");
                        }

                        if (productUOMResponse.Result?.UomSecondary != null)
                        {
                            transactionItemRequest.ProductUOMSecondaryMCode = productUOMResponse.Result.UomSecondary.Code;
                        }

                        // Validate that EffectivedProductPrice exists before price validation
                        if (productUOMResponse.Result?.EffectivedProductPrice == null)
                        {
                            throw new ValidationException($"ProductUOM with ID {transactionItemRequest.ProductUOMId} does not have an effective product price.");
                        }

                        // Validate that payload ProductPriceId matches ProductUOM's EffectivedProductPrice Id
                        if (transactionItemRequest.ProductPriceId != productUOMResponse.Result.EffectivedProductPrice.Id)
                        {
                            throw new ValidationException($"ProductPriceId {transactionItemRequest.ProductPriceId} does not match the effective product price Id {productUOMResponse.Result.EffectivedProductPrice.Id} for ProductUOM {transactionItemRequest.ProductUOMId}.");
                        }

                        // Validate quantity is within OrderMinQty and OrderMaxQty range
                        if (productUOMResponse.Result.OrderMinQty.HasValue && transactionItemRequest.Quantity < productUOMResponse.Result.OrderMinQty.Value)
                        {
                            throw new ValidationException($"Quantity {transactionItemRequest.Quantity} is below the minimum order quantity {productUOMResponse.Result.OrderMinQty.Value} for ProductUOM {transactionItemRequest.ProductUOMId}.");
                        }

                        if (productUOMResponse.Result.OrderMaxQty.HasValue && transactionItemRequest.Quantity > productUOMResponse.Result.OrderMaxQty.Value)
                        {
                            throw new ValidationException($"Quantity {transactionItemRequest.Quantity} is above the maximum order quantity {productUOMResponse.Result.OrderMaxQty.Value} for ProductUOM {transactionItemRequest.ProductUOMId}.");
                        }

                        // Validate price based on PriceEditable setting
                        if (!productUOMResponse.Result.PriceEditable)
                        {
                            // If PriceEditable is false, check if UnitAmount matches EffectivedProductPrice's price
                            if (transactionItemRequest.UnitAmount != productUOMResponse.Result.EffectivedProductPrice.Price)
                            {
                                throw new ValidationException($"UnitAmount {transactionItemRequest.UnitAmount} does not match the fixed price {productUOMResponse.Result.EffectivedProductPrice.Price} for ProductUOM {transactionItemRequest.ProductUOMId}.");
                            }
                        }
                        else
                        {
                            // If PriceEditable is true, check if UnitAmount is within MinEditPrice and MaxEditPrice range
                            if (productUOMResponse.Result.MinEditPrice.HasValue && transactionItemRequest.UnitAmount < productUOMResponse.Result.MinEditPrice.Value)
                            {
                                throw new ValidationException($"UnitAmount {transactionItemRequest.UnitAmount} is below the minimum allowed price {productUOMResponse.Result.MinEditPrice.Value} for ProductUOM {transactionItemRequest.ProductUOMId}.");
                            }

                            if (productUOMResponse.Result.MaxEditPrice.HasValue && transactionItemRequest.UnitAmount > productUOMResponse.Result.MaxEditPrice.Value)
                            {
                                throw new ValidationException($"UnitAmount {transactionItemRequest.UnitAmount} is above the maximum allowed price {productUOMResponse.Result.MaxEditPrice.Value} for ProductUOM {transactionItemRequest.ProductUOMId}.");
                            }
                        }
                    }
                }

                if (isCalculationRequired)
                {
                    int? serviceCharge = currentUser?.Company?.ServiceCharge != null ? int.Parse(currentUser.Company.ServiceCharge.ToString()) : null;

                    var transactionCalculate = new TransactionCalculate();
                    foreach (var transactionItemRequest in reqBody.TransactionItemRequest)
                    {
                        var calculationTransactionItem = await CalculationUpsertTransactionItem(transactionItemRequest, (Guid)getCurrentUser.Item2);
                        _mapper.Map(transactionItemRequest, transactionItemRequest);

                        transactionCalculate.SalesTaxAmount += calculationTransactionItem.SalesTaxAmount;
                        transactionCalculate.ServiceTaxAmount += calculationTransactionItem.ServiceTaxAmount;
                        transactionCalculate.TaxExclAmount += calculationTransactionItem.TaxExclAmount;
                        transactionCalculate.TaxInclAmount += calculationTransactionItem.TaxInclAmount;
                        transactionCalculate.TotalUnitAmountWOTax += calculationTransactionItem.TotalUnitAmountWOTax;
                        transactionCalculate.TotalUnitAmount += calculationTransactionItem.TotalUnitAmount;
                        transactionCalculate.SubTotalAmount += calculationTransactionItem.SubTotalAmount;
                    }

                    reqBody.TotalExclTax = transactionCalculate.TaxExclAmount;
                    reqBody.TotalInclTax = transactionCalculate.TaxInclAmount;
                    reqBody.TotalTaxAmount = transactionCalculate.SubTotalAmount;
                    reqBody.TotalSalesTaxAmount = transactionCalculate.SalesTaxAmount != 0.0m ? transactionCalculate.SalesTaxAmount : null;
                    reqBody.TotalServiceTaxAmount = transactionCalculate.ServiceTaxAmount != 0.0m ? transactionCalculate.ServiceTaxAmount : null;
                    reqBody.TotalServiceChargePerc = serviceCharge;
                    reqBody.TotalServiceChargeAmount = serviceCharge != null ? Math.Round(transactionCalculate.TotalUnitAmountWOTax * decimal.Parse(serviceCharge.ToString()) / 100m, 2, MidpointRounding.AwayFromZero) : 0.00m;

                    (decimal grandTotalAmount, decimal grandTotalAdjustmentAmount) = SharedFunctionHelper.CalculateRoundingAdjustment(transactionCalculate.SubTotalAmount);
                    reqBody.TotalAmount = grandTotalAmount;
                    reqBody.TotalRoundingAdjustmentAmount = grandTotalAdjustmentAmount;
                }

                reqBody.UserId = currentUser.Id;
                result = await _transactionService.UpdateTransaction(reqBody, dbContext);
                if (!result.IsSuccessful)
                {
                    _logger.LogError(result.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                    throw new Exception(result.Exception);
                }

                // Get existing transaction items for comparison
                var existingTransactionItems = existingTransactionResponse.Result.TransactionItem.Where(x => x.IsActive).ToList();
                var requestTransactionItemIds = reqBody.TransactionItemRequest
                    .Where(x => x.Id.HasValue && x.Id != Guid.Empty)
                    .Select(x => x.Id.Value)
                    .ToList();

                // Handle transaction items
                var maxOrderGroup = existingTransactionItems.Max(x => x.OrderGroup) + 1;
                foreach (var transactionItemRequest in reqBody.TransactionItemRequest)
                {
                    if (transactionItemRequest.Id == null || transactionItemRequest.Id == Guid.Empty)
                    {
                        // Create new transaction item
                        transactionItemRequest.OrderGroup = maxOrderGroup;
                        var newTransactionItemRequest = _mapper.Map<TransactionItemRequest>(transactionItemRequest);
                        result = await _transactionService.InsertTransactionItem(newTransactionItemRequest, dbContext);
                        if (!result.IsSuccessful)
                        {
                            _logger.LogError(result.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                            throw new Exception(transactionItemRequest.ProductId + " " + result.Exception);
                        }
                    }
                    else
                    {
                        // Check if transaction item exists
                        var existingItem = existingTransactionItems.FirstOrDefault(x => x.Id == transactionItemRequest.Id);
                        if (existingItem == null)
                        {
                            throw new ValidationException($"Transaction item with ID {transactionItemRequest.Id} not found.");
                        }

                        // Update existing transaction item
                        result = await _transactionService.UpdateTransactionItem(reqBody.Id, transactionItemRequest, dbContext);
                        if (!result.IsSuccessful)
                        {
                            _logger.LogError(result.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                            throw new Exception(transactionItemRequest.ProductId + " " + result.Exception);
                        }
                    }
                }

                // Delete transaction items that are not in the request (soft delete)
                var itemsToDelete = existingTransactionItems
                    .Where(x => !requestTransactionItemIds.Contains(x.Id))
                    .ToList();

                foreach (var itemToDelete in itemsToDelete)
                {
                    var deleteResult = await _transactionService.DeleteTransactionItem(itemToDelete.Id, dbContext);
                    if (!deleteResult.IsSuccessful)
                    {
                        _logger.LogError(deleteResult.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                        throw new Exception($"Failed to delete transaction item {itemToDelete.Id}: " + deleteResult.Exception);
                    }
                }
                await transaction.CommitAsync();
            }
            catch (ValidationException ex)
            {
                await transaction.RollbackAsync();
                // Handle validation errors with BadRequest status
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(
                    result,
                    Enums.StatusCode.BadRequest,
                    exception: ex
                );
                _logger.LogError(ex, $"Validation error in {MethodBase.GetCurrentMethod().ReflectedType.Name}: {ex.Message}");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                await transaction.DisposeAsync();
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, result);
        }

        [HttpPost("Transaction/Delete")]
        public async Task<IActionResult> DeleteTransaction(Guid TransactionId)
        {
            var userAccessValidatation = await _userService.UserAccessValidatation(true);
            if (!userAccessValidatation.IsSuccessful)
                return ActionResultResponse<NoResult, ResultId>(_mapper, userAccessValidatation);

            var response = await _transactionService.DeleteTransaction(TransactionId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<NoResult, NoResult>(_mapper, response);
        }
        #endregion

        #region TransactionPaid
        [HttpGet("TransactionPaid")]
        [RequirePermission(Permissions.Transaction.View)]
        public async Task<IActionResult> GetTransactionPaidList([FromQuery] Pagination pagination, [FromQuery] CommonFilterList commonFilter, [FromQuery] Guid? transactionId)
        {
            var response = await _transactionService.GetTransactionPaidList(pagination, commonFilter, transactionId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<TransactionPaidDto>, PagedList<TransactionPaid>>(_mapper, response, pagination, PagedList<TransactionPaid>.PagedMetadata(response));
        }

        [HttpGet("TransactionPaid/Get/{transactionPaidId}")]
        [RequirePermission(Permissions.Transaction.View)]
        public async Task<IActionResult> GetTransactionPaidByGuid(Guid transactionPaidId)
        {
            if (transactionPaidId == Guid.Empty)
            {
                return BadRequest(new { Message = "Transaction Payment ID is required." });
            }

            var response = await _transactionService.GetTransactionPaidByGuid(transactionPaidId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<TransactionPaidDto, TransactionPaid>(_mapper, response);
        }

        [HttpPost("TransactionPaid/Create")]
        [RequirePermission(Permissions.Transaction.Create)]
        public async Task<IActionResult> CreateTransactionPaid([FromBody] TransactionPaidRequest reqBody)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            var dbContext = _transactionService.CreateDbContext();
            var transaction = dbContext.Database.BeginTransaction();

            try
            {
                // Validate request
                if (reqBody == null)
                {
                    throw new ValidationException("Request body cannot be null.");
                }

                if (reqBody.PaidAmount <= 0)
                {
                    throw new ValidationException("Paid amount must be greater than 0.");
                }

                if (reqBody.PaymentTypeId == Guid.Empty)
                {
                    throw new ValidationException("Payment type ID is required.");
                }

                if (reqBody.TrxId == Guid.Empty)
                {
                    throw new ValidationException("Transaction ID is required.");
                }

                // Validate transaction exists
                var transactionResponse = await _transactionService.GetTransactionByGuid(reqBody.TrxId);
                if (!transactionResponse.IsSuccessful)
                    throw new ValidationException(transactionResponse.Exception);
                else if (transactionResponse.Result == null || transactionResponse.Result.Id == Guid.Empty)
                    throw new ValidationException($"Transaction with ID {reqBody.TrxId} not found.");

                // Validate payment type exists
                var paymentTypeResponse = await _transactionService.GetPaymentTypeByGuid(reqBody.PaymentTypeId);
                if (!paymentTypeResponse.IsSuccessful)
                    throw new ValidationException(paymentTypeResponse.Exception);
                else if (paymentTypeResponse.Result == null || paymentTypeResponse.Result.Id == Guid.Empty)
                    throw new ValidationException($"Payment type with ID {reqBody.PaymentTypeId} not found.");

                // Insert the transaction payment
                result = await _transactionService.InsertTransactionPaid(reqBody, dbContext);
                if (!result.IsSuccessful)
                {
                    _logger.LogError(result.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                    throw new Exception(result.Exception);
                }

                await transaction.CommitAsync();
            }
            catch (ValidationException ex)
            {
                await transaction.RollbackAsync();
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.BadRequest, exception: ex);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                await transaction.DisposeAsync();
            }

            return ActionResultResponse<ResultId, ResultId>(_mapper, result);
        }

        //[HttpPut("TransactionPaid/Update/{transactionPaidId}")]
        //public async Task<IActionResult> UpdateTransactionPaid(Guid transactionPaidId, [FromBody] UpdateTransactionPaidRequest reqBody)
        //{
        //    var result = new BaseResponse<ResultId> { IsSuccessful = true };
        //    var dbContext = _transactionService.CreateDbContext();
        //    var transaction = dbContext.Database.BeginTransaction();

        //    try
        //    {
        //        // Validate request
        //        if (reqBody == null)
        //        {
        //            throw new ValidationException("Request body cannot be null.");
        //        }

        //        if (transactionPaidId == Guid.Empty)
        //        {
        //            throw new ValidationException("Transaction Payment ID is required.");
        //        }

        //        // Set the ID from the route parameter
        //        reqBody.Id = transactionPaidId;

        //        if (reqBody.PaidAmount <= 0)
        //        {
        //            throw new ValidationException("Paid amount must be greater than 0.");
        //        }

        //        if (reqBody.PaymentTypeId == Guid.Empty)
        //        {
        //            throw new ValidationException("Payment type ID is required.");
        //        }

        //        if (reqBody.TrxId == Guid.Empty)
        //        {
        //            throw new ValidationException("Transaction ID is required.");
        //        }

        //        // Validate payment type exists
        //        var paymentTypeResponse = await _transactionService.GetPaymentTypeByGuid(reqBody.PaymentTypeId);
        //        if (!paymentTypeResponse.IsSuccessful)
        //            throw new ValidationException(paymentTypeResponse.Exception);
        //        else if (paymentTypeResponse.Result == null || paymentTypeResponse.Result.Id == Guid.Empty)
        //            throw new ValidationException($"Payment type with ID {reqBody.PaymentTypeId} not found.");

        //        // Validate transaction paid exists
        //        var transactionPaidResponse = await _transactionService.GetTransactionPaidByGuid(transactionPaidId);
        //        if (!transactionPaidResponse.IsSuccessful)
        //            throw new ValidationException(transactionPaidResponse.Exception);
        //        else if (transactionPaidResponse.Result == null || transactionPaidResponse.Result.Id == Guid.Empty)
        //            throw new ValidationException($"Transaction payment with ID {transactionPaidId} not found.");

        //        // Validate transaction exists
        //        var transactionResponse = await _transactionService.GetTransactionByGuid(reqBody.TrxId);
        //        if (!transactionResponse.IsSuccessful)
        //            throw new ValidationException(transactionResponse.Exception);
        //        else if (transactionResponse.Result == null || transactionResponse.Result.Id == Guid.Empty)
        //            throw new ValidationException($"Transaction with ID {reqBody.TrxId} not found.");

        //        // Update the transaction payment
        //        result = await _transactionService.UpdateTransactionPaid(reqBody, dbContext);
        //        if (!result.IsSuccessful)
        //        {
        //            _logger.LogError(result.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
        //            throw new Exception(result.Exception);
        //        }

        //        await transaction.CommitAsync();
        //    }
        //    catch (ValidationException ex)
        //    {
        //        await transaction.RollbackAsync();
        //        result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.BadRequest, exception: ex);
        //    }
        //    catch (Exception ex)
        //    {
        //        await transaction.RollbackAsync();
        //        result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
        //    }
        //    finally
        //    {
        //        await transaction.DisposeAsync();
        //    }

        //    return ActionResultResponse<ResultId, ResultId>(_mapper, result);
        //}

        [HttpDelete("TransactionPaid/Delete/{transactionPaidId}")]
        [RequirePermission(Permissions.Transaction.Delete)]
        public async Task<IActionResult> DeleteTransactionPaid(Guid transactionPaidId)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            var dbContext = _transactionService.CreateDbContext();
            var transaction = dbContext.Database.BeginTransaction();

            try
            {
                // Validate request
                if (transactionPaidId == Guid.Empty)
                {
                    throw new ValidationException("Transaction Payment ID is required.");
                }

                // Validate transaction paid exists
                var transactionPaidResponse = await _transactionService.GetTransactionPaidByGuid(transactionPaidId);
                if (!transactionPaidResponse.IsSuccessful)
                    throw new ValidationException(transactionPaidResponse.Exception);
                else if (transactionPaidResponse.Result == null || transactionPaidResponse.Result.Id == Guid.Empty)
                    throw new ValidationException($"Transaction payment with ID {transactionPaidId} not found.");

                // Delete the transaction payment
                result = await _transactionService.DeleteTransactionPaid(transactionPaidId, dbContext);
                if (!result.IsSuccessful)
                {
                    _logger.LogError(result.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                    throw new Exception(result.Exception);
                }

                await transaction.CommitAsync();
            }
            catch (ValidationException ex)
            {
                await transaction.RollbackAsync();
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.BadRequest, exception: ex);
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            finally
            {
                await transaction.DisposeAsync();
            }

            return ActionResultResponse<NoResult, NoResult>(_mapper, result);
        }
        #endregion

        #region PaymentType
        [HttpGet("PaymentType")]
        [RequirePermission(Permissions.Transaction.View)]
        public async Task<IActionResult> GetPaymentTypeList([FromQuery] Pagination pagination, [FromQuery] CommonFilterList commonFilter)
        {
            var response = await _transactionService.GetPaymentTypeList(pagination, commonFilter);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<List<PaymentTypeDto>, PagedList<PaymentType>>(_mapper, response, pagination, PagedList<PaymentType>.PagedMetadata(response));
        }

        [HttpGet("PaymentType/Get/{paymentTypeId}")]
        [RequirePermission(Permissions.Transaction.View)]
        public async Task<IActionResult> GetPaymentTypeByGuid(Guid paymentTypeId)
        {
            if (paymentTypeId == Guid.Empty)
            {
                return BadRequest(new { Message = "Payment Type ID is required." });
            }

            var response = await _transactionService.GetPaymentTypeByGuid(paymentTypeId);
            if (!response.IsSuccessful)
            {
                _logger.LogError(response.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
            }
            return ActionResultResponse<PaymentTypeDto, PaymentType>(_mapper, response);
        }
        #endregion

        private async Task<TransactionCalculate> CalculationTransactionItem(TransactionItemRequest transactionItemRequest, Guid branchId)
        {
            var transactionItemCalculate = new TransactionCalculate();
            try
            {
                var productResponse = await _productService.GetProductByGuid(transactionItemRequest.ProductId);
                if (!productResponse.IsSuccessful)
                {
                    _logger.LogError(productResponse.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                    throw new Exception(transactionItemRequest.ProductId + " not found. " + productResponse.Exception);
                }
                else if (productResponse.Result == null)
                {
                    throw new Exception(transactionItemRequest.ProductId + " not found. ");
                }

                var product = productResponse.Result;
                if (!product.IsTaxExempt)
                {
                    TaxRate salesTaxNo = null;
                    TaxRate serviceTaxNo = null;
                    var branchResponse = await _companyService.GetBranchByGuid(branchId);
                    if (!branchResponse.IsSuccessful || branchResponse.Result == null)
                    {
                        throw new Exception("Branch not found.");
                    }

                    if (product.CustomSalesTaxNoId != null)
                    {
                        var salesTaxNoResponse = await _companyService.GetTaxRateByGuid((Guid)product.CustomSalesTaxNoId);
                        if (!salesTaxNoResponse.IsSuccessful || salesTaxNoResponse.Result == null)
                        {
                            throw new Exception("Product custom sales tax no id " + product.CustomSalesTaxNoId + " not found. ");
                        }
                        salesTaxNo = salesTaxNoResponse.Result;
                    }
                    else
                    {
                        if (branchResponse.Result?.Company?.DefaultSalesTaxNo == null)
                        {
                            throw new Exception("Branch default sales tax no not found.");
                        }
                        salesTaxNo = branchResponse.Result.Company.DefaultSalesTaxNo;
                    }

                    if (product.CustomServiceTaxNoId != null)
                    {
                        var serviceTaxNoResponse = await _companyService.GetTaxRateByGuid((Guid)product.CustomServiceTaxNoId);
                        if (!serviceTaxNoResponse.IsSuccessful)
                        {
                            throw new Exception("Product custom sales tax no id " + product.CustomServiceTaxNoId + " not found. ");
                        }
                        serviceTaxNo = serviceTaxNoResponse.Result;
                    }
                    else if (salesTaxNo == null)
                    {
                        if (branchResponse.Result?.Company?.DefaultServiceTaxNo == null)
                        {
                            throw new Exception("Branch default service tax no not found.");
                        }
                        serviceTaxNo = branchResponse.Result.Company.DefaultServiceTaxNo;
                    }

                    if (salesTaxNo == null && serviceTaxNo == null)
                    {
                        throw new Exception("Branch default sales tax and service tax no not found.");
                    }

                    int? salesTaxRate = salesTaxNo != null ? salesTaxNo.ChargePercentage : null;
                    int? serviceTaxRate = serviceTaxNo != null ? serviceTaxNo.ChargePercentage : null;
                    (decimal salesTaxAmount, decimal serviceTaxAmount, decimal taxExclAmount, decimal taxInclAmount, decimal totalUnitAmountWOTax, decimal totalUnitAmount, decimal subTotalAmount) = SharedFunctionHelper.CalculateTax(transactionItemRequest.UnitAmount, transactionItemRequest.Quantity, salesTaxRate, serviceTaxRate, product.IsTaxExcl);

                    transactionItemCalculate.SalesTaxAmount = salesTaxAmount;
                    transactionItemCalculate.ServiceTaxAmount = serviceTaxAmount;
                    transactionItemCalculate.TaxExclAmount = taxExclAmount;
                    transactionItemCalculate.TaxInclAmount = taxInclAmount;
                    transactionItemCalculate.TotalUnitAmountWOTax = totalUnitAmountWOTax;
                    transactionItemCalculate.TotalUnitAmount = totalUnitAmount;
                    transactionItemCalculate.SubTotalAmount = subTotalAmount;
                }
                else
                {
                    transactionItemCalculate.TotalUnitAmountWOTax = transactionItemRequest.TotalUnitAmountWOTax;
                    transactionItemCalculate.TotalUnitAmount = transactionItemRequest.TotalUnitAmount;
                    transactionItemCalculate.SubTotalAmount = transactionItemRequest.SubTotalAmount;
                }

                //transactionItemCalculate.ExckTax
                return transactionItemCalculate;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private async Task<TransactionCalculate> CalculationUpsertTransactionItem(UpsertTransactionItemRequest transactionItemRequest, Guid branchId)
        {
            var transactionItemCalculate = new TransactionCalculate();
            try
            {
                var productResponse = await _productService.GetProductByGuid(transactionItemRequest.ProductId);
                if (!productResponse.IsSuccessful)
                {
                    _logger.LogError(productResponse.Exception, $"Error occured in {MethodBase.GetCurrentMethod().ReflectedType.Name}");
                    throw new Exception(transactionItemRequest.ProductId + " not found. " + productResponse.Exception);
                }
                else if (productResponse.Result == null)
                {
                    throw new Exception(transactionItemRequest.ProductId + " not found. ");
                }

                var product = productResponse.Result;
                if (!product.IsTaxExempt)
                {
                    TaxRate salesTaxNo = null;
                    TaxRate serviceTaxNo = null;
                    var branchResponse = await _companyService.GetBranchByGuid(branchId);
                    if (!branchResponse.IsSuccessful || branchResponse.Result == null)
                    {
                        throw new Exception("Branch not found.");
                    }
                    if (product.CustomSalesTaxNoId != null)
                    {
                        var salesTaxNoResponse = await _companyService.GetTaxRateByGuid((Guid)product.CustomSalesTaxNoId);
                        if (!salesTaxNoResponse.IsSuccessful || salesTaxNoResponse.Result == null)
                        {
                            throw new Exception("Product custom sales tax no id " + product.CustomSalesTaxNoId + " not found. ");
                        }
                        salesTaxNo = salesTaxNoResponse.Result;
                    }
                    else
                    {
                        if (branchResponse.Result?.Company?.DefaultSalesTaxNo == null)
                        {
                            throw new Exception("Branch default sales tax no not found.");
                        }
                        salesTaxNo = branchResponse.Result.Company.DefaultSalesTaxNo;
                    }

                    if (product.CustomServiceTaxNoId != null)
                    {
                        var serviceTaxNoResponse = await _companyService.GetTaxRateByGuid((Guid)product.CustomServiceTaxNoId);
                        if (!serviceTaxNoResponse.IsSuccessful)
                        {
                            throw new Exception("Product custom sales tax no id " + product.CustomServiceTaxNoId + " not found. ");
                        }
                        serviceTaxNo = serviceTaxNoResponse.Result;
                    }
                    else if (salesTaxNo == null)
                    {
                        if (branchResponse.Result?.Company?.DefaultServiceTaxNo == null)
                        {
                            throw new Exception("Branch default service tax no not found.");
                        }
                        serviceTaxNo = branchResponse.Result.Company.DefaultServiceTaxNo;
                    }

                    (decimal salesTaxAmount, decimal serviceTaxAmount, decimal taxExclAmount, decimal taxInclAmount, decimal totalUnitAmountWOTax, decimal totalUnitAmount, decimal subTotalAmount) = SharedFunctionHelper.CalculateTax(transactionItemRequest.UnitAmount, transactionItemRequest.Quantity, (salesTaxNo != null ? salesTaxNo.ChargePercentage : null), (serviceTaxNo != null ? serviceTaxNo.ChargePercentage : null), product.IsTaxExcl);
                    transactionItemCalculate.SalesTaxAmount = salesTaxAmount;
                    transactionItemCalculate.ServiceTaxAmount = serviceTaxAmount;
                    transactionItemCalculate.TaxExclAmount = taxExclAmount;
                    transactionItemCalculate.TaxInclAmount = taxInclAmount;
                    transactionItemCalculate.TotalUnitAmountWOTax = totalUnitAmountWOTax;
                    transactionItemCalculate.TotalUnitAmount = totalUnitAmount;
                    transactionItemCalculate.SubTotalAmount = subTotalAmount;
                }
                else
                {
                    transactionItemCalculate.TotalUnitAmountWOTax = transactionItemRequest.TotalUnitAmountWOTax;
                    transactionItemCalculate.TotalUnitAmount = transactionItemRequest.TotalUnitAmount;
                    transactionItemCalculate.SubTotalAmount = transactionItemRequest.SubTotalAmount;
                }

                //transactionItemCalculate.ExckTax
                return transactionItemCalculate;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
