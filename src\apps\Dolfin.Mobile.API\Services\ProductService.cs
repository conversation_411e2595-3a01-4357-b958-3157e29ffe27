﻿using AutoMapper;
using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.InkML;
using DocumentFormat.OpenXml.Wordprocessing;
using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Models;
using Dolfin.Utility.Utils;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using System.Linq.Expressions;
using static Dolfin.Utility.Constant.Constant;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Services
{
    public class ProductService : BaseComponent<Product>, IProductService
    {
        private readonly StandardMessage _standardMessage;
        private readonly IUserService _userService;
        private readonly IInventoryService _inventoryService;
        private readonly ILogger<ProductService> _logger;
        private readonly IMapper _mapper;

        // Method to expose GetDbContext to controllers
        public DolfinDbContext GetDbContext() => base.GetDbContext();

        public ProductService(
            DbContextOptions<DolfinDbContext> dbContextOptions,
            ISettingsService settingService,
            IUserService userService,
            IInventoryService inventoryService,
            ILoggerFactory loggerFactory,
            IMapper mapper) : base(dbContextOptions)
        {
            _standardMessage = new StandardMessage();
            _userService = userService;
            _inventoryService = inventoryService;
            _logger = loggerFactory.CreateLogger<ProductService>();
            _mapper = mapper;
        }


        #region Product Categories
        public async Task<BaseResponse<PagedList<ProductCategory>>> GetProductCategoryList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null)
        {
            var result = new BaseResponse<PagedList<ProductCategory>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync(companyId);
                var currentUser = getCurrentUser.Item1;
                companyId = getCurrentUser.Item2;

                Expression<Func<ProductCategory, bool>> predicate = x => (x.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<ProductCategory>()
                    .Include(u => u.Company)
                    .Where(predicate)
                    .Where(u => u.Company.IsActive)
                    .AsQueryable();

                var response = await query.ToListAsync();
                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<ProductCategory>, PagedList<ProductCategory>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ProductCategory>> GetProductCategoryByGuid(Guid productCategoryId)
        {
            var result = new BaseResponse<ProductCategory> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = currentUser.CompanyId;

                var query = GetDbContext().Set<ProductCategory>()
                    .Include(u => u.Company)
                    .Include(u => u.Product.Where(x => x.IsActive)).ThenInclude(u => u.Classification)
                    .Where(x => x.Id == productCategoryId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(x => x.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(u => u.Company.IsActive)
                    .AsQueryable();
                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ProductCategory, ProductCategory>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertProductCategory(ProductCategoryRequest reqBody)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(reqBody);
                var currentUser = getCurrentUser.Item1;
                reqBody = getCurrentUser.Item2;

                var newProductCategory = _mapper.Map<ProductCategory>(reqBody);
                newProductCategory.IsActive = true;
                newProductCategory.CreatedAt = DateTime.UtcNow;
                newProductCategory.CreatedBy = Guid.Parse(currentUser.Id);
                var productUOM = await CreateAsync(newProductCategory);
                result.Result = new ResultId { Id = productUOM.Id };
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateProductCategory(UpdateProductCategoryRequest reqBody)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(reqBody);
                var currentUser = getCurrentUser.Item1;
                reqBody = getCurrentUser.Item2;

                var productCategory = await GetProductCategoryByGuid(reqBody.Id);
                if (!productCategory.IsSuccessful)
                    throw new Exception(productCategory.Exception);
                else if (productCategory == null || productCategory.Result?.Id == null)
                    throw new Exception($"{reqBody.Id} not found.");
                else
                {
                    var existingProductCategory = productCategory.Result;

                    // Update properties directly
                    if (reqBody.Name != null)
                        existingProductCategory.Name = reqBody.Name;

                    if (reqBody.Description != null)
                        existingProductCategory.Description = reqBody.Description;

                    if (reqBody.DisplayOrder.HasValue)
                        existingProductCategory.DisplayOrder = reqBody.DisplayOrder;

                    if (reqBody.Published.HasValue)
                        existingProductCategory.Published = reqBody.Published.Value;

                    // CompanyId should not be updated as it's a key relationship

                    // Update audit fields
                    existingProductCategory.UpdatedAt = DateTime.UtcNow;
                    existingProductCategory.UpdatedBy = Guid.Parse(currentUser.Id);

                    await UpdateAsync(existingProductCategory);
                    result.Result = new ResultId { Id = existingProductCategory.Id };
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<NoResultResponse> DeleteProductCategory(Guid id)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            try
            {
                var productUOM = await GetProductCategoryByGuid(id);
                if (!productUOM.IsSuccessful)
                    throw new Exception(productUOM.Exception);
                if (productUOM == null || productUOM.Result?.Id == null)
                    throw new Exception($"{id} not found.");

                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())) && currentUser.CompanyId != productUOM.Result.CompanyId)
                    throw new Exception($"{id} not found.");

                // Use the Result property directly instead of trying to map
                var deleteProductCategory = productUOM.Result;
                deleteProductCategory.IsActive = false;
                deleteProductCategory.UpdatedAt = DateTime.UtcNow;
                deleteProductCategory.UpdatedBy = Guid.Parse(currentUser.Id);
                await UpdateAsync(deleteProductCategory);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
        #endregion


        #region Product UOM
        public async Task<BaseResponse<PagedList<ProductUOM>>> GetProductUOMList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null)
        {
            var result = new BaseResponse<PagedList<ProductUOM>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync(companyId);
                var currentUser = getCurrentUser.Item1;
                companyId = getCurrentUser.Item2;

                Expression<Func<ProductUOM, bool>> predicate = x => (x.Product.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var today = DateTime.UtcNow;
                var query = GetDbContext().Set<ProductUOM>()
                    .Include(u => u.UomPrimary)
                    .Include(u => u.UomSecondary)
                    .Include(u => u.Product).ThenInclude(u => u.Company)
                    .Include(u => u.Product).ThenInclude(u => u.Classification)
                    .Include(u => u.ProductPrice.Where(x => x.IsActive))
                    .Where(predicate)
                    .Where(u => u.UomPrimary.IsActive)
                    .Where(u => u.UomSecondary == null || u.UomSecondary.IsActive)
                    .Where(u => u.Product.IsActive)
                    .Where(u => u.Product.Company.IsActive)
                    .Where(u => u.Product.Classification == null || u.Product.Classification.IsActive)
                    .AsQueryable();

                var response = await query.ToListAsync();

                foreach (var uom in response)
                {
                    uom.EffectivedProductPrice = uom.ProductPrice
                        .Where(pp => pp.IsActive && pp.EffectiveAt <= today)
                        .OrderByDescending(pp => pp.EffectiveAt)
                        .FirstOrDefault();
                }
                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<ProductUOM>, PagedList<ProductUOM>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ProductUOM>> GetProductUOMByGuid(Guid productUOMId, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ProductUOM> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;
                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                    var currentUser = getCurrentUser.Item1;
                    var companyId = currentUser.CompanyId;

                    var query = dbContext.Set<ProductUOM>()
                        .Include(u => u.UomPrimary)
                        .Include(u => u.UomSecondary)
                        .Include(u => u.Product).ThenInclude(u => u.Company)
                        .Include(u => u.Product).ThenInclude(u => u.Classification)
                        .Include(u => u.ProductPrice.Where(x => x.IsActive))
                        .Where(x => x.Id == productUOMId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        .Where(x => x.Product.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        .Where(u => u.UomPrimary.IsActive)
                        .Where(u => u.UomSecondary == null || u.UomSecondary.IsActive)
                        .Where(u => u.Product.IsActive)
                        .Where(u => u.Product.Company.IsActive)
                        .Where(u => u.Product.Classification == null || u.Product.Classification.IsActive)
                        .AsQueryable();
                    result.Result = await query.FirstOrDefaultAsync();

                    // Populate EffectivedProductPrice if it's null
                    if (result.Result != null)
                    {
                        var today = DateTime.UtcNow;
                        result.Result.EffectivedProductPrice = result.Result.ProductPrice
                            .Where(pp => pp.IsActive && pp.EffectiveAt <= today)
                            .OrderByDescending(pp => pp.EffectiveAt)
                            .FirstOrDefault();
                    }

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ProductUOM, ProductUOM>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<ProductUOM>> GetProductUOMByCode(string productUOMCode, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ProductUOM> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                    var currentUser = getCurrentUser.Item1;
                    var companyId = currentUser.CompanyId;

                    var query = dbContext.Set<ProductUOM>()
                        .Include(u => u.UomPrimary)
                        .Include(u => u.UomSecondary)
                        .Include(u => u.Product).ThenInclude(u => u.Company)
                        .Include(u => u.Product).ThenInclude(u => u.Classification)
                        .Where(x => x.Code == productUOMCode && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        .Where(x => x.Product.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        .Where(u => u.UomPrimary.IsActive)
                        .Where(u => u.UomSecondary == null || u.UomSecondary.IsActive)
                        .Where(u => u.Product.IsActive)
                        .Where(u => u.Product.Company.IsActive)
                        .Where(u => u.Product.Classification == null || u.Product.Classification.IsActive)
                        .AsQueryable();
                    result.Result = await query.FirstOrDefaultAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ProductUOM, ProductUOM>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<ProductUOM>> GetProductUOMByProductAndUomId(Guid productId, Guid uomId, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ProductUOM> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;
                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                    var currentUser = getCurrentUser.Item1;
                    var companyId = currentUser.CompanyId;

                    var query = dbContext.Set<ProductUOM>()
                        .Include(u => u.UomPrimary)
                        .Include(u => u.UomSecondary)
                        .Include(u => u.Product).ThenInclude(u => u.Company)
                        .Include(u => u.Product).ThenInclude(u => u.Classification)
                        .Where(x => x.ProductId == productId && x.UomPrimaryId == uomId &&
                               (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        .Where(x => x.Product.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        .Where(u => u.UomPrimary.IsActive)
                        .Where(u => u.UomSecondary == null || u.UomSecondary.IsActive)
                        .Where(u => u.Product.IsActive)
                        .Where(u => u.Product.Company.IsActive)
                        .Where(u => u.Product.Classification == null || u.Product.Classification.IsActive)
                        .AsQueryable();

                    result.Result = await query.FirstOrDefaultAsync();

                    if (result.Result == null)
                    {
                        throw new Exception($"ProductUOM with ProductId {productId} and UomId {uomId} not found.");
                    }
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ProductUOM, ProductUOM>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        private async Task<BaseResponse<List<ProductUOM>>> GetProductUOMByProductId(Guid productId)
        {
            var result = new BaseResponse<List<ProductUOM>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var companyId = currentUser.CompanyId;

                var query = GetDbContext().Set<ProductUOM>()
                    .Include(u => u.UomPrimary)
                    .Include(u => u.UomSecondary)
                    .Include(u => u.Product).ThenInclude(u => u.Company)
                    .Include(u => u.Product).ThenInclude(u => u.Classification)
                    .Where(x => x.ProductId == productId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(x => x.Product.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(u => u.UomPrimary.IsActive)
                    .Where(u => u.UomSecondary == null || u.UomSecondary.IsActive)
                    .Where(u => u.Product.IsActive)
                    .Where(u => u.Product.Company.IsActive)
                    .Where(u => u.Product.Classification == null || u.Product.Classification.IsActive)
                    .AsQueryable();
                result.Result = await query.ToListAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<List<ProductUOM>, List<ProductUOM>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertProductUOM(ProductUOMRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;
                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync();

                    // Validate Fraction is at least 1
                    if (reqBody.Fraction < 1)
                    {
                        throw new Exception("Fraction must be at least 1");
                    }

                    if (reqBody.IsMainUom)
                    {
                        await UpdateIsMainUOM(currentUser, reqBody.ProductId, dbContext);
                    }

                    var newProductUOM = _mapper.Map<ProductUOM>(reqBody);
                    newProductUOM.IsActive = true;
                    newProductUOM.CreatedAt = DateTime.UtcNow;
                    newProductUOM.CreatedBy = Guid.Parse(currentUser.Id);
                    var productUOM = await CreateAsync(newProductUOM, dbContext);
                    result.Result = new ResultId { Id = productUOM.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateProductUOM(UpdateProductUOMRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(reqBody);
                    var currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    var productUOM = await GetProductUOMByGuid(reqBody.Id, dbContext);
                    if (!productUOM.IsSuccessful)
                        throw new Exception(productUOM.Exception);
                    else if (productUOM == null || productUOM.Result?.Id == null)
                        throw new Exception($"{reqBody.Id} not found.");

                    if (reqBody.IsMainUom != null && reqBody.IsMainUom.Value != productUOM.Result.IsMainUom)
                    {
                        if (reqBody.IsMainUom.Value)
                        {
                            await UpdateIsMainUOM(currentUser, productUOM.Result.ProductId, dbContext);
                        }
                        else
                        {
                            throw new Exception($"{reqBody.Id} no access to change Is Main UOM."); //no allow change to false, only allow change to true. If change to false then no more Main UOM
                        }
                    }

                    var existingProductUOM = productUOM.Result;

                    // Update properties directly
                    existingProductUOM.Name = reqBody.Name ?? existingProductUOM.Name;
                    existingProductUOM.Description = reqBody.Description ?? existingProductUOM.Description;

                    // Code, Barcode, and Fraction are not included in UpdateProductUOMRequest

                    // Cost and PreviousCost are not included in UpdateProductUOMRequest

                    if (reqBody.OrderMinQty.HasValue)
                        existingProductUOM.OrderMinQty = reqBody.OrderMinQty;

                    if (reqBody.OrderMaxQty.HasValue)
                        existingProductUOM.OrderMaxQty = reqBody.OrderMaxQty;

                    if (reqBody.MinEditPrice.HasValue)
                        existingProductUOM.MinEditPrice = reqBody.MinEditPrice;

                    if (reqBody.MaxEditPrice.HasValue)
                        existingProductUOM.MaxEditPrice = reqBody.MaxEditPrice;

                    // Boolean properties
                    if (reqBody.IsMainUom != null)
                        existingProductUOM.IsMainUom = reqBody.IsMainUom.Value;

                    // IsPriceFollowUomMainId is not included in UpdateProductUOMRequest

                    if (reqBody.PriceEditable != null)
                        existingProductUOM.PriceEditable = reqBody.PriceEditable.Value;

                    // UomSecondaryId is not included in UpdateProductUOMRequest

                    // Update audit fields
                    existingProductUOM.UpdatedAt = DateTime.UtcNow;
                    existingProductUOM.UpdatedBy = Guid.Parse(currentUser.Id);

                    await UpdateAsync(existingProductUOM, dbContext);
                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                    result.Result = new ResultId { Id = existingProductUOM.Id };
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        private async Task UpdateIsMainUOM(ApplicationUser currentUser, Guid productId, DolfinDbContext dbContextRollback = null)
        {
            try
            {
                var productMainUOMList = await GetProductUOMByProductId(productId);
                if (!productMainUOMList.IsSuccessful)
                    throw new Exception(productMainUOMList.Exception);
                else if (productMainUOMList == null || productMainUOMList.Result.Count == 0)
                    return;
                else
                {
                    var productMainUOM = productMainUOMList.Result.FindAll(x => x.IsMainUom);
                    if (productMainUOM.Count > 0)
                    {
                        throw new Exception($"{productId} more than one Is Main UOM as true.");
                    }
                    var updateProductMainUOM = new ProductUOM();
                    updateProductMainUOM.Id = productMainUOM[0].Id;
                    updateProductMainUOM.IsMainUom = false;

                    updateProductMainUOM.UpdatedAt = DateTime.UtcNow;
                    updateProductMainUOM.UpdatedBy = Guid.Parse(currentUser.Id);
                    await UpdateAsync(updateProductMainUOM, dbContextRollback);

                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        public async Task<NoResultResponse> DeleteProductUOM(Guid id)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            try
            {
                var productUOM = await GetProductUOMByGuid(id, null);
                if (!productUOM.IsSuccessful)
                    throw new Exception(productUOM.Exception);
                if (productUOM == null || productUOM.Result?.Id == null)
                    throw new Exception($"{id} not found.");

                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())) && currentUser.CompanyId != productUOM.Result.Product.CompanyId)
                    throw new Exception($"{id} not found.");

                // Use the Result property directly instead of trying to map
                var deleteProductUOM = productUOM.Result;
                deleteProductUOM.IsActive = false;
                deleteProductUOM.UpdatedAt = DateTime.UtcNow;
                deleteProductUOM.UpdatedBy = Guid.Parse(currentUser.Id);
                await UpdateAsync(deleteProductUOM);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
        #endregion


        #region Product
        public async Task<BaseResponse<PagedList<Product>>> GetProductList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null)
        {
            var result = new BaseResponse<PagedList<Product>> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync(companyId);
                var currentUser = getCurrentUser.Item1;
                companyId = getCurrentUser.Item2;
                var branchId = currentUser.BranchId;



                Expression<Func<Product, bool>> predicate = x => (x.CompanyId == companyId || (companyId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                        && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))))
                        && (x.InventoryProduct.Any(x => x.Inventory.BranchId == branchId) || (branchId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<Product>()
                .Include(p => p.ProductCategory)
                .Include(p => p.ProductCostMethod)
                .Include(p => p.CustomSalesTaxNo)
                .Include(p => p.CustomServiceTaxNo)
                .Include(p => p.Company)
                .Include(p => p.Classification)
                .Include(p => p.Currency)
                .Include(p => p.ProductUOM.Where(x => x.IsActive)).ThenInclude(u => u.UomPrimary)
                .Include(p => p.ProductUOM.Where(x => x.IsActive)).ThenInclude(u => u.UomSecondary)
                .Include(p => p.ProductUOM.Where(x => x.IsActive)).ThenInclude(u => u.ProductPrice.Where(x => x.IsActive))
                .Include(p => p.InventoryProduct.Where(x => x.IsActive)).ThenInclude(u => u.Inventory).ThenInclude(u => u.Branch)
                .Include(p => p.InventoryItem.Where(x => x.IsActive))
                .Where(predicate) // Apply the main predicate at the database level
                .Where(p => p.ProductCategory.IsActive)
                .Where(p => p.ProductCostMethod == null || p.ProductCostMethod.IsActive)
                .Where(p => p.CustomSalesTaxNo == null || p.CustomSalesTaxNo.IsActive)
                .Where(p => p.CustomServiceTaxNo == null || p.CustomServiceTaxNo.IsActive)
                .Where(p => p.Company.IsActive)
                .Where(p => p.Classification == null || p.Classification.IsActive)
                .Where(p => p.Currency.IsActive)
                .AsQueryable();

                //var response = await query.ToListAsync();
                var response = query
                    .AsEnumerable() // Move data to memory to allow in-memory filtering
                    .Select(p => new Product
                    {
                        // Base domain fields
                        Id = p.Id,
                        IsActive = p.IsActive,
                        CreatedAt = p.CreatedAt,
                        CreatedBy = p.CreatedBy,
                        UpdatedAt = p.UpdatedAt,
                        UpdatedBy = p.UpdatedBy,

                        // Product specific fields
                        Code = p.Code,
                        Name = p.Name,
                        Description = p.Description,
                        IsTaxExempt = p.IsTaxExempt,
                        IsTaxExcl = p.IsTaxExcl,
                        CustomSalesTaxNoId = p.CustomSalesTaxNoId ?? currentUser.Company.DefaultSalesTaxNoId,
                        CustomServiceTaxNoId = p.CustomServiceTaxNoId ?? currentUser.Company.DefaultServiceTaxNoId,
                        Sku = p.Sku,
                        Image = p.Image,
                        Weight = p.Weight,
                        Length = p.Length,
                        Width = p.Width,
                        Height = p.Height,
                        AccountCode = p.AccountCode,
                        AvailableStartAt = p.AvailableStartAt,
                        AvailableEndAt = p.AvailableEndAt,
                        DisplayOrder = p.DisplayOrder,
                        Published = p.Published,
                        CompanyId = p.CompanyId,
                        ProductCategoryId = p.ProductCategoryId,
                        ProductCostMethodId = p.ProductCostMethodId,
                        ClassificationId = p.ClassificationId,
                        CurrencyId = p.CurrencyId,

                        // Navigation properties
                        Company = p.Company,
                        ProductCategory = p.ProductCategory,
                        ProductCostMethod = p.ProductCostMethod,
                        CustomSalesTaxNo = p.CustomSalesTaxNo,
                        CustomServiceTaxNo = p.CustomServiceTaxNo,
                        Classification = p.Classification,
                        ProductUOM = p.ProductUOM,
                        Currency = p.Currency,

                        // Set filtered Inventory based on branch logic
                        InventoryProduct = p.InventoryProduct
                            .Where(i => branchId == null || i.Inventory.BranchId == branchId) // Apply branch filter
                            .GroupBy(i => i.ProductId)
                            .Select(g => g.OrderBy(i => i.Inventory.Branch == null ? 1 : 0).FirstOrDefault())
                            .Where(i => i != null)
                            .ToList()
                    })
                    .ToList();

                // Set EffectivedProductPrice for each ProductUOM
                var today = DateTime.UtcNow;
                foreach (var product in response)
                {
                    if (product.ProductUOM != null)
                    {
                        foreach (var uom in product.ProductUOM)
                        {
                            uom.EffectivedProductPrice = uom.ProductPrice
                                .Where(pp => pp.IsActive && pp.EffectiveAt <= today)
                                .OrderByDescending(pp => pp.EffectiveAt)
                                .FirstOrDefault();
                        }
                    }
                }



                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<Product>, PagedList<Product>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<Product>> GetProductByGuid(Guid productId, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<Product> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;
                var branchId = currentUser.BranchId;

                // Use the provided dbContext or get a new one
                var dbContext = dbContextRollback ?? GetDbContext();

                var query = dbContext.Set<Product>()
                .Include(p => p.ProductCategory)
                .Include(p => p.ProductCostMethod)
                .Include(p => p.CustomSalesTaxNo)
                .Include(p => p.CustomServiceTaxNo)
                .Include(p => p.Company)
                .Include(p => p.Classification)
                .Include(p => p.Currency)
                .Include(p => p.ProductUOM.Where(x => x.IsActive)).ThenInclude(u => u.UomPrimary)
                .Include(p => p.ProductUOM.Where(x => x.IsActive)).ThenInclude(u => u.UomSecondary)
                .Include(p => p.ProductUOM.Where(x => x.IsActive)).ThenInclude(u => u.ProductPrice.Where(x => x.IsActive))
                .Include(p => p.InventoryProduct.Where(x => x.IsActive)).ThenInclude(u => u.Inventory).ThenInclude(u => u.Branch)
                .Include(p => p.InventoryItem.Where(x => x.IsActive))
                .Where(x => x.Id == productId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                .Where(p => p.ProductCategory.IsActive)
                .Where(p => p.ProductCostMethod == null || p.ProductCostMethod.IsActive)
                .Where(p => p.CustomSalesTaxNo == null || p.CustomSalesTaxNo.IsActive)
                .Where(p => p.CustomServiceTaxNo == null || p.CustomServiceTaxNo.IsActive)
                .Where(p => p.Company.IsActive)
                .Where(p => p.Classification == null || p.Classification.IsActive)
                .Where(p => p.Currency.IsActive)
                .AsQueryable();

                var response = query
                    .AsEnumerable() // Move data to memory to allow in-memory filtering
                    .Select(p => new Product
                    {
                        // Base domain fields
                        Id = p.Id,
                        IsActive = p.IsActive,
                        CreatedAt = p.CreatedAt,
                        CreatedBy = p.CreatedBy,
                        UpdatedAt = p.UpdatedAt,
                        UpdatedBy = p.UpdatedBy,

                        // Product specific fields
                        Code = p.Code,
                        Name = p.Name,
                        Description = p.Description,
                        IsTaxExempt = p.IsTaxExempt,
                        IsTaxExcl = p.IsTaxExcl,
                        CustomSalesTaxNoId = p.CustomSalesTaxNoId,
                        CustomServiceTaxNoId = p.CustomServiceTaxNoId,
                        Sku = p.Sku,
                        Image = p.Image,
                        Weight = p.Weight,
                        Length = p.Length,
                        Width = p.Width,
                        Height = p.Height,
                        AccountCode = p.AccountCode,
                        AvailableStartAt = p.AvailableStartAt,
                        AvailableEndAt = p.AvailableEndAt,
                        DisplayOrder = p.DisplayOrder,
                        Published = p.Published,
                        CompanyId = p.CompanyId,
                        ProductCategoryId = p.ProductCategoryId,
                        ProductCostMethodId = p.ProductCostMethodId,
                        ClassificationId = p.ClassificationId,

                        // Navigation properties
                        Company = p.Company,
                        ProductCategory = p.ProductCategory,
                        ProductCostMethod = p.ProductCostMethod,
                        CustomSalesTaxNo = p.CustomSalesTaxNo,
                        CustomServiceTaxNo = p.CustomServiceTaxNo,
                        Classification = p.Classification,
                        ProductUOM = p.ProductUOM,
                        Currency = p.Currency,

                        // Set filtered Inventory based on branch logic
                        InventoryProduct = p.InventoryProduct
                            .Where(i => i.Inventory.BranchId == branchId || (branchId == null && SharedFunctionHelper.PermissionViewLimitCompanyOrBranchAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId))))) // Apply branch filter
                            .GroupBy(i => i.ProductId)
                            .Select(g => g.OrderBy(i => i.Inventory.BranchId == null ? 1 : 0).FirstOrDefault())
                            .Where(i => i != null)
                            .ToList()
                    })
                    .ToList();

                // Set EffectivedProductPrice for each ProductUOM
                var today = DateTime.UtcNow;
                foreach (var product in response)
                {
                    if (product.ProductUOM != null)
                    {
                        foreach (var uom in product.ProductUOM)
                        {
                            uom.EffectivedProductPrice = uom.ProductPrice
                                .Where(pp => pp.IsActive && pp.EffectiveAt <= today)
                                .OrderByDescending(pp => pp.EffectiveAt)
                                .FirstOrDefault();
                        }
                    }
                }

                result.Result = response.FirstOrDefault();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Product, Product>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertProduct(ProductRequest reqBody, ApplicationUser currentUser, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;
                try
                {
                    // Create the product
                    var newProduct = _mapper.Map<Product>(reqBody);
                    newProduct.IsActive = true;
                    newProduct.CreatedAt = DateTime.UtcNow;
                    newProduct.CreatedBy = Guid.Parse(currentUser.Id);
                    var product = await CreateAsync(newProduct, dbContext);
                    result.Result = new ResultId { Id = product.Id };

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }

            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateProduct(UpdateProductRequest reqBody, ApplicationUser currentUser, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    // Get current user info from the request body
                    var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(reqBody);
                    currentUser = getCurrentUser.Item1;
                    reqBody = getCurrentUser.Item2;

                    // Get the product to update directly from the database context
                    var product = await GetProductByGuid(reqBody.Id, dbContext);
                    if (!product.IsSuccessful)
                        throw new Exception(product.Exception);
                    else if (product == null || product.Result?.Id == null)
                        throw new Exception($"{reqBody.Id} not found.");

                    // Check user permission (only admin or users from the same company can update)
                    if (currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString()))
                        && currentUser.CompanyId != product.Result.CompanyId)
                    {
                        throw new Exception($"You don't have permission to update this product.");
                    }

                    var existingProduct = product.Result;

                    // Update properties directly
                    existingProduct.Name = reqBody.Name ?? existingProduct.Name;
                    existingProduct.Description = reqBody.Description ?? existingProduct.Description;

                    if (reqBody.CustomSalesTaxNo.HasValue)
                        existingProduct.CustomSalesTaxNoId = reqBody.CustomSalesTaxNo.Value;

                    if (reqBody.CustomServiceTaxNo.HasValue)
                        existingProduct.CustomServiceTaxNoId = reqBody.CustomServiceTaxNo.Value;

                    if (reqBody.Weight.HasValue)
                        existingProduct.Weight = reqBody.Weight;

                    if (reqBody.Length.HasValue)
                        existingProduct.Length = reqBody.Length;

                    if (reqBody.Width.HasValue)
                        existingProduct.Width = reqBody.Width;

                    if (reqBody.Height.HasValue)
                        existingProduct.Height = reqBody.Height;

                    if (reqBody.AvailableStartAt.HasValue)
                        existingProduct.AvailableStartAt = reqBody.AvailableStartAt;

                    if (reqBody.AvailableEndAt.HasValue)
                        existingProduct.AvailableEndAt = reqBody.AvailableEndAt;

                    if (reqBody.DisplayOrder.HasValue)
                        existingProduct.DisplayOrder = reqBody.DisplayOrder;

                    if (reqBody.Published.HasValue)
                        existingProduct.Published = reqBody.Published.Value;

                    if (reqBody.ProductCategoryId.HasValue && reqBody.ProductCategoryId.Value != Guid.Empty)
                        existingProduct.ProductCategoryId = reqBody.ProductCategoryId.Value;

                    existingProduct.ClassificationId = reqBody.ClassificationId;

                    // Update audit fields
                    existingProduct.UpdatedAt = DateTime.UtcNow;
                    existingProduct.UpdatedBy = Guid.Parse(currentUser.Id);

                    // Save the updated product
                    await UpdateAsync(existingProduct, dbContext);

                    // Set success result
                    result.Result = new ResultId { Id = existingProduct.Id };

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }

            return result;
        }

        public async Task<NoResultResponse> DeleteProduct(Guid id, DolfinDbContext dbContextRollback = null)
        {
            var result = new NoResultResponse { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;

                try
                {
                    var product = await GetProductByGuid(id, dbContext);
                    if (!product.IsSuccessful)
                        throw new Exception(product.Exception);
                    if (product == null || product.Result?.Id == null)
                        throw new Exception($"{id} not found.");

                    var currentUser = await _userService.GetCurrentUserAsync();
                    if (currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())) && currentUser.CompanyId != product.Result.CompanyId)
                        throw new Exception($"{id} not found.");

                    // Use the Result property directly instead of trying to map
                    var deleteProduct = product.Result;
                    deleteProduct.IsActive = false;
                    deleteProduct.UpdatedAt = DateTime.UtcNow;
                    deleteProduct.UpdatedBy = Guid.Parse(currentUser.Id);
                    await UpdateAsync(deleteProduct, dbContext);

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }
        #endregion

        #region Product Cost Method
        public async Task<BaseResponse<PagedList<ProductCostMethod>>> GetProductCostMethodList(Pagination pagination = null, CommonFilterList filterList = null)
        {
            var result = new BaseResponse<PagedList<ProductCostMethod>> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();

                // Apply the main predicate at the database level
                Expression<Func<ProductCostMethod, bool>> predicate = x => x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<ProductCostMethod>()
                .Where(predicate)
                .AsQueryable();

                var response = await query.ToListAsync();

                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<ProductCostMethod>, PagedList<ProductCostMethod>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ProductCostMethod>> GetProductCostMethodByGuid(Guid productCostMethodId)
        {
            var result = new BaseResponse<ProductCostMethod> { IsSuccessful = true };
            try
            {

                var getCurrentUser = await _userService.GetCurrentUserStdGetWithCompanyAsync();
                var currentUser = getCurrentUser.Item1;

                var query = GetDbContext().Set<ProductCostMethod>()
                .Where(x => x.Id == productCostMethodId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                .AsQueryable();

                //var response = query
                //    .AsEnumerable() // Move data to memory to allow in-memory filtering
                //    .Select(p => new ProductCostMethod
                //    {
                //        Id = p.Id,
                //        Code = p.Code,
                //        Name = p.Name,
                //        Description = p.Description
                //    })
                //    .ToList();
                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ProductCostMethod, ProductCostMethod>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertProductCostMethod(ProductCostMethodRequest reqBody)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(reqBody);
                var currentUser = getCurrentUser.Item1;
                reqBody = getCurrentUser.Item2;

                var newProductCostMethod = _mapper.Map<ProductCostMethod>(reqBody);
                newProductCostMethod.IsActive = true;
                newProductCostMethod.CreatedAt = DateTime.UtcNow;
                newProductCostMethod.CreatedBy = Guid.Parse(currentUser.Id);
                var productCostMethod = await CreateAsync(newProductCostMethod);
                result.Result = new ResultId { Id = productCostMethod.Id };
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateProductCostMethod(UpdateProductCostMethodRequest reqBody)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var getCurrentUser = await _userService.GetCurrentUserStdUpsertWithCompanyAsync(reqBody);
                var currentUser = getCurrentUser.Item1;
                reqBody = getCurrentUser.Item2;

                var ProductCostMethod = await GetProductCostMethodByGuid(reqBody.Id);
                if (!ProductCostMethod.IsSuccessful)
                    throw new Exception(ProductCostMethod.Exception);
                else if (ProductCostMethod == null || ProductCostMethod.Result?.Id == null)
                    throw new Exception($"{reqBody.Id} not found.");
                else
                {
                    var existingProductCostMethod = ProductCostMethod.Result;

                    // Update properties
                    existingProductCostMethod.Name = reqBody.Name ?? existingProductCostMethod.Name;
                    existingProductCostMethod.Description = reqBody.Description ?? existingProductCostMethod.Description;

                    existingProductCostMethod.UpdatedAt = DateTime.UtcNow;
                    existingProductCostMethod.UpdatedBy = Guid.Parse(currentUser.Id);

                    await UpdateAsync(existingProductCostMethod);
                    result.Result = new ResultId { Id = existingProductCostMethod.Id };
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<NoResultResponse> DeleteProductCostMethod(Guid id)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            try
            {
                var productCostMethod = await GetProductCostMethodByGuid(id);
                if (!productCostMethod.IsSuccessful)
                    throw new Exception(productCostMethod.Exception);
                if (productCostMethod == null || productCostMethod.Result?.Id == null)
                    throw new Exception($"{id} not found.");

                var currentUser = await _userService.GetCurrentUserAsync();
                if (currentUser.UserRoles.Any(x => !x.RoleId.Equals(UserTypeEnum.Admin.GetAmbientValue().Value.ToString())))
                    throw new Exception($"{id} not found.");

                // Use the Result property directly instead of trying to map
                var deleteProductCostMethod = productCostMethod.Result;
                deleteProductCostMethod.IsActive = false;
                deleteProductCostMethod.UpdatedAt = DateTime.UtcNow;
                deleteProductCostMethod.UpdatedBy = Guid.Parse(currentUser.Id);
                await UpdateAsync(deleteProductCostMethod);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
        #endregion

        #region  UOM
        public async Task<BaseResponse<PagedList<UOM>>> GetUOMList(Pagination pagination = null, CommonFilterList filterList = null)
        {
            var result = new BaseResponse<PagedList<UOM>> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();

                // Apply the main predicate at the database level
                Expression<Func<UOM, bool>> predicate = x => x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<UOM>()
                    .Include(u => u.UOMCategory)
                    .Where(predicate)
                    .Where(u => u.UOMCategory.IsActive)
                    .AsQueryable();

                var response = await query.ToListAsync();

                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<UOM>, PagedList<UOM>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<UOM>> GetUOMByGuid(Guid uomId)
        {
            var result = new BaseResponse<UOM> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();

                var query = GetDbContext().Set<UOM>()
                    .Include(u => u.UOMCategory)
                    .Where(x => x.Id == uomId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(u => u.UOMCategory.IsActive)
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<UOM, UOM>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertUOM(UOMRequest reqBody)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserStdUpsertByAdminAsync();

                var newUOM = _mapper.Map<UOM>(reqBody);
                newUOM.IsActive = true;
                newUOM.CreatedAt = DateTime.UtcNow;
                newUOM.CreatedBy = Guid.Parse(currentUser.Id);
                var uom = await CreateAsync(newUOM);
                result.Result = new ResultId { Id = uom.Id };
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateUOM(UpdateUOMRequest reqBody)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserStdUpsertByAdminAsync();

                var UOM = await GetUOMByGuid(reqBody.Id);
                if (!UOM.IsSuccessful)
                    throw new Exception(UOM.Exception);
                else if (UOM == null || UOM.Result?.Id == null)
                    throw new Exception($"{reqBody.Id} not found.");
                else
                {
                    var existingUOM = UOM.Result;

                    // Update properties
                    existingUOM.Name = reqBody.Name ?? existingUOM.Name;
                    existingUOM.Description = reqBody.Description ?? existingUOM.Description;
                    // Only update UOMCategoryId if it's not empty
                    if (reqBody.UOMCategoryId != Guid.Empty)
                    {
                        existingUOM.UOMCategoryId = reqBody.UOMCategoryId;
                    }

                    existingUOM.UpdatedAt = DateTime.UtcNow;
                    existingUOM.UpdatedBy = Guid.Parse(currentUser.Id);

                    await UpdateAsync(existingUOM);
                    result.Result = new ResultId { Id = existingUOM.Id };
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<NoResultResponse> DeleteUOM(Guid id)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            try
            {
                var uom = await GetUOMByGuid(id);
                if (!uom.IsSuccessful)
                    throw new Exception(uom.Exception);
                if (uom == null || uom.Result?.Id == null)
                    throw new Exception($"{id} not found.");

                var currentUser = await _userService.GetCurrentUserStdUpsertByAdminAsync();

                // Use the Result property directly instead of trying to map
                var deleteUOM = uom.Result;
                deleteUOM.IsActive = false;
                deleteUOM.UpdatedAt = DateTime.UtcNow;
                deleteUOM.UpdatedBy = Guid.Parse(currentUser.Id);
                await UpdateAsync(deleteUOM);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
        #endregion

        #region  UOM Categories
        public async Task<BaseResponse<PagedList<UOMCategories>>> GetUOMCategoryList(Pagination pagination = null, CommonFilterList filterList = null)
        {
            var result = new BaseResponse<PagedList<UOMCategories>> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();

                // Apply the main predicate at the database level
                Expression<Func<UOMCategories, bool>> predicate = x => x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)));

                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<UOMCategories>()
                    .Where(predicate)
                    .AsQueryable();

                var response = await query.ToListAsync();

                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<UOMCategories>, PagedList<UOMCategories>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<UOMCategories>> GetUOMCategoryByGuid(Guid uomCategoryId)
        {
            var result = new BaseResponse<UOMCategories> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();

                var query = GetDbContext().Set<UOMCategories>()
                .Where(x => x.Id == uomCategoryId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                .AsQueryable();

                //var response = query
                //    .AsEnumerable() // Move data to memory to allow in-memory filtering
                //    .Select(p => new UOMCategories
                //    {
                //        Id = p.Id,
                //        Code = p.Code,
                //        Name = p.Name,
                //        Description = p.Description
                //    })
                //    .ToList();
                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<UOMCategories, UOMCategories>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
        #endregion

        #region Classification
        public async Task<BaseResponse<Classification>> GetClassificationByGuid(Guid classificationId)
        {
            var result = new BaseResponse<Classification> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();
                UserTypeEnum? userType = null;
                if (currentUser != null)
                    userType = Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId));

                var query = GetDbContext().Set<Classification>()
                    .Where(x => x.Id == classificationId &&
                           (x.IsActive || (userType != null && SharedFunctionHelper.PermissionViewAll((UserTypeEnum)userType))))
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<Classification, Classification>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
        #endregion

        #region Product Price
        public async Task<BaseResponse<PagedList<ProductPrice>>> GetProductPriceList(Pagination pagination = null, CommonFilterList filterList = null, Guid? productUOMId = null)
        {
            var result = new BaseResponse<PagedList<ProductPrice>> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();

                // Apply the main predicate at the database level
                Expression<Func<ProductPrice, bool>> predicate = x => x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)));

                // Add productUOMId filter if provided
                if (productUOMId.HasValue)
                {
                    var productUOMIdValue = productUOMId.Value;
                    predicate = predicate.AndAlso(x => x.ProductUOMId == productUOMIdValue);
                }

                // Add common filters if provided
                if (filterList != null && filterList.FilterList != null && filterList.FilterList.Count > 0)
                    predicate = ExpressionExtensions.BuildPredicate(predicate, filterList);

                var query = GetDbContext().Set<ProductPrice>()
                    .Include(x => x.ProductUOM)
                    .Where(predicate)
                    .Where(x => x.ProductUOM.IsActive)
                    .AsQueryable();

                var response = await query.ToListAsync();

                result.Result = SharedFunctionHelper.StandardPagination(response, pagination);
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<PagedList<ProductPrice>, PagedList<ProductPrice>>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ProductPrice>> GetProductPriceByGuid(Guid productPriceId)
        {
            var result = new BaseResponse<ProductPrice> { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserAsync();

                var query = GetDbContext().Set<ProductPrice>()
                    .Include(x => x.ProductUOM)
                    .Where(x => x.Id == productPriceId && (x.IsActive || SharedFunctionHelper.PermissionViewAll(Enums.GetValueFromAmbient<UserTypeEnum>(Guid.Parse(currentUser.UserRoles.First().RoleId)))))
                    .Where(x => x.ProductUOM.IsActive)
                    .AsQueryable();

                result.Result = await query.FirstOrDefaultAsync();
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<ProductPrice, ProductPrice>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> InsertProductPrice(ProductPriceRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;
                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync();

                    // Validate ProductUOMId
                    var productUOM = await GetProductUOMByGuid(reqBody.ProductUOMId, dbContext);
                    if (!productUOM.IsSuccessful || productUOM.Result == null)
                    {
                        throw new Exception($"Product UOM with ID {reqBody.ProductUOMId} not found.");
                    }

                    // Validate FractionQty is at least 1
                    if (reqBody.FractionQty < 1)
                    {
                        throw new Exception($"FractionQty must be at least 1. Current value: {reqBody.FractionQty}");
                    }

                    var newProductPrice = _mapper.Map<ProductPrice>(reqBody);
                    newProductPrice.EffectiveAt = reqBody.EffectiveAt ?? DateTime.UtcNow;
                    newProductPrice.IsActive = true;
                    newProductPrice.CreatedAt = DateTime.UtcNow;
                    newProductPrice.CreatedBy = Guid.Parse(currentUser.Id);

                    var productPrice = await CreateAsync(newProductPrice, dbContext);
                    result.Result = new ResultId { Id = productPrice.Id };

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<BaseResponse<ResultId>> UpdateProductPrice(UpdateProductPriceRequest reqBody, DolfinDbContext dbContextRollback = null)
        {
            var result = new BaseResponse<ResultId> { IsSuccessful = true };

            bool isNewDbContext = dbContextRollback == null;
            var dbContext = dbContextRollback ?? GetDbContext();
            try
            {
                var currentTransaction = dbContext.Database.CurrentTransaction;
                var transaction = currentTransaction == null ? dbContext.Database.BeginTransaction() : currentTransaction;
                try
                {
                    var currentUser = await _userService.GetCurrentUserAsync();

                    var productPrice = await GetProductPriceByGuid(reqBody.Id);
                    if (!productPrice.IsSuccessful)
                        throw new Exception(productPrice.Exception);
                    else if (productPrice.Result == null)
                        throw new Exception($"Product Price with ID {reqBody.Id} not found.");
                    else
                    {
                        var existingProductPrice = productPrice.Result;

                        // Update properties
                        existingProductPrice.FractionQty = reqBody.FractionQty;
                        existingProductPrice.Price = reqBody.Price;
                        existingProductPrice.EffectiveAt = reqBody.EffectiveAt ?? existingProductPrice.EffectiveAt;
                        existingProductPrice.Remark = reqBody.Remark ?? existingProductPrice.Remark;

                        existingProductPrice.UpdatedAt = DateTime.UtcNow;
                        existingProductPrice.UpdatedBy = Guid.Parse(currentUser.Id);

                        await UpdateAsync(existingProductPrice, dbContext);
                        result.Result = new ResultId { Id = existingProductPrice.Id };
                    }

                    if (currentTransaction == null)
                        await transaction.CommitAsync();
                }
                catch (Exception ex)
                {
                    if (currentTransaction == null)
                        await transaction.RollbackAsync();
                    result = _standardMessage.ErrorMessage<ResultId, ResultId>(result, Enums.StatusCode.InternalServerError, exception: ex);
                }
                finally
                {
                    if (currentTransaction == null)
                        await transaction.DisposeAsync();
                }
            }
            finally
            {
                if (isNewDbContext)
                    await dbContext.DisposeAsync();
            }
            return result;
        }

        public async Task<NoResultResponse> DeleteProductPrice(Guid id)
        {
            var result = new NoResultResponse { IsSuccessful = true };
            try
            {
                var currentUser = await _userService.GetCurrentUserStdUpsertByAdminAsync();

                var productPrice = await GetProductPriceByGuid(id);
                if (!productPrice.IsSuccessful)
                    throw new Exception(productPrice.Exception);
                else if (productPrice.Result == null)
                    throw new Exception($"Product Price with ID {id} not found.");
                else
                {
                    var existingProductPrice = productPrice.Result;
                    existingProductPrice.IsActive = false;
                    existingProductPrice.UpdatedAt = DateTime.UtcNow;
                    existingProductPrice.UpdatedBy = Guid.Parse(currentUser.Id);
                    await UpdateAsync(existingProductPrice);
                }
            }
            catch (Exception ex)
            {
                result = _standardMessage.ErrorMessage<NoResult, NoResult>(result, Enums.StatusCode.InternalServerError, exception: ex);
            }
            return result;
        }
        #endregion
    }
}
