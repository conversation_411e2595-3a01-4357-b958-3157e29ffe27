using Dolfin.Framework.Data.Domains;
using Dolfin.Mobile.API.Models.UBLInvoice;
using System.Security.Cryptography;
using System.Security.Cryptography.Xml;
using System.Text;
using System.Xml;
using System.Xml.Serialization;

namespace Dolfin.Mobile.API.Helper
{
    public static class LhdnXmlHelper
    {
        /// <summary>
        /// Generates XML document for LHDN eInvoice submission
        /// </summary>
        /// <param name="transaction">Transaction data</param>
        /// <param name="documentType">Document type</param>
        /// <param name="documentTypeVersion">Document type version (should be "1.0" for LHDN)</param>
        /// <param name="invoiceTypeCode">Invoice type code (e.g., "01" for standard invoice)</param>
        /// <param name="invoicePeriodStartDate">Start date for the invoice period (defaults to transaction date)</param>
        /// <param name="invoicePeriodEndDate">End date for the invoice period (defaults to 30 days after start date)</param>
        /// <param name="invoicePeriodDescription">Description for the invoice period (defaults to "Monthly")</param>
        /// <returns>XML document as string</returns>
        public static string GenerateInvoiceXml(
            Transaction transaction,
            string documentType,
            string documentTypeVersion = "1.0", // Default to version 1.0 as required by LHDN
            string invoiceTypeCode = "01")
        {
            // Create UBL Invoice object
            var invoice = new Invoice
            {
                ID = transaction.TrxNo,
                IssueDate = transaction.TrxDatetime.ToString("yyyy-MM-dd"),
                IssueTime = transaction.TrxDatetime.ToString("HH:mm:ss'Z'"),
                InvoiceTypeCode = new InvoiceTypeCodeType
                {
                    ListVersionID = documentTypeVersion,
                    Value = invoiceTypeCode
                },
                DocumentCurrencyCode = "MYR", // Malaysian Ringgit
                TaxCurrencyCode = transaction.CurrencyCode,
                // Add BillingReference
                BillingReference = new BillingReference
                {
                    AdditionalDocumentReference = new AdditionalDocumentReference
                    {
                        ID = transaction.Branch?.Company?.RegNo
                    }
                },
                // Add AdditionalDocumentReferences
                AdditionalDocumentReferences = new List<AdditionalDocumentReference>
                {
                    new AdditionalDocumentReference
                    {
                        ID = "L1",
                        DocumentType = "CustomsImportForm"
                    }
                }
            };

            // Add seller information (from company/branch)
            var accountingSupplierParty = new AccountingSupplierParty
            {
                AdditionalAccountID = new AdditionalAccountIDType
                {
                    SchemeAgencyName = "CertEX",
                    Value = transaction.Branch?.Company?.TinNo
                },
                Party = new Party
                {
                    IndustryClassificationCode = "46510", // Example code for IT industry
                    PartyIdentifications = new List<PartyIdentification>
                    {
                        new PartyIdentification { ID = new IDType { SchemeID = "TIN", Value = transaction.Branch?.Company?.TinNo } },
                        new PartyIdentification { ID = new IDType { SchemeID = "BRN", Value = transaction.Branch?.Company?.RegNo } },
                        new PartyIdentification { ID = new IDType { SchemeID = "SST", Value = transaction.Branch?.Company?.SstNo ?? "NA" } },
                        new PartyIdentification { ID = new IDType { SchemeID = "TTX", Value = "NA" } }
                    },
                    PartyName = new PartyName { Name = transaction.Branch?.Company?.Name },
                    PostalAddress = new PostalAddress
                    {
                        StreetName = transaction.Branch?.Address?.Address1,
                        CityName = transaction.Branch?.Address?.Region?.Name,
                        PostalZone = transaction.Branch?.Address?.PostalCode,
                        CountrySubentityCode = transaction.Branch?.Address?.State?.Abbreviation ?? "14", // Default to Kuala Lumpur
                        AddressLines = new List<AddressLine>
                        {
                            new AddressLine { Line = transaction.Branch?.Address?.Address1 },
                            new AddressLine { Line = transaction.Branch?.Address?.Address2 },
                            new AddressLine { Line = transaction.Branch?.Address?.Address3 }
                        },
                        Country = new Models.UBLInvoice.Country
                        {
                            IdentificationCode = new IdentificationCodeType
                            {
                                ListID = "ISO3166-1",
                                ListAgencyID = "6",
                                Value = "MYS"
                            }
                        }
                    },
                    PartyTaxScheme = new PartyTaxScheme
                    {
                        CompanyID = transaction.Branch?.Company?.TinNo,
                        TaxScheme = new TaxScheme
                        {
                            ID = new TaxSchemeIDType
                            {
                                Value = "SST"
                            }
                        }
                    },
                    PartyLegalEntity = new PartyLegalEntity
                    {
                        RegistrationName = transaction.Branch?.Company?.Name,
                        CompanyID = transaction.Branch?.Company?.RegNo
                    },
                    Contact = new Contact
                    {
                        Telephone = transaction.Branch?.Address?.PhoneNo,
                        ElectronicMail = transaction.Branch?.Address?.Email
                    }
                }
            };
            invoice.AccountingSupplierParty = accountingSupplierParty;

            // Add buyer information (from customer)
            var accountingCustomerParty = new AccountingCustomerParty
            {
                Party = new Party
                {
                    PartyIdentifications = new List<PartyIdentification>
                    {
                        new PartyIdentification { ID = new IDType { SchemeID = "TIN", Value = transaction.Customer?.TinNo ?? "Buyer's TIN" } },
                        new PartyIdentification { ID = new IDType { SchemeID = "BRN", Value = transaction.Customer?.IdentityNo ?? "Buyer's BRN" } },
                        new PartyIdentification { ID = new IDType { SchemeID = "SST", Value = "NA" } },
                        new PartyIdentification { ID = new IDType { SchemeID = "TTX", Value = "NA" } }
                    },
                    PartyName = new PartyName { Name = transaction.Customer?.Name },
                    PostalAddress = new PostalAddress
                    {
                        StreetName = transaction.BillingAddress?.Address1,
                        CityName = transaction.BillingAddress?.Region?.Name,
                        PostalZone = transaction.BillingAddress?.PostalCode,
                        CountrySubentityCode = transaction.BillingAddress?.State?.Abbreviation ?? "14", // Default to Kuala Lumpur
                        AddressLines = new List<AddressLine>
                        {
                            new AddressLine { Line = transaction.BillingAddress?.Address1 },
                            new AddressLine { Line = transaction.BillingAddress?.Address2 },
                            new AddressLine { Line = transaction.BillingAddress?.Address3 }
                        },
                        Country = new Models.UBLInvoice.Country
                        {
                            IdentificationCode = new IdentificationCodeType
                            {
                                ListID = "ISO3166-1",
                                ListAgencyID = "6",
                                Value = "MYS"
                            }
                        }
                    },
                    PartyTaxScheme = new PartyTaxScheme
                    {
                        CompanyID = transaction.Customer?.TinNo,
                        TaxScheme = new TaxScheme
                        {
                            ID = new TaxSchemeIDType
                            {
                                Value = "SST"
                            }
                        }
                    },
                    PartyLegalEntity = new PartyLegalEntity
                    {
                        RegistrationName = transaction.Customer?.Name,
                        CompanyID = transaction.Customer?.IdentityNo
                    },
                    Contact = new Contact
                    {
                        Telephone = transaction.BillingAddress?.PhoneNo,
                        ElectronicMail = transaction.BillingAddress?.Email
                    }
                }
            };
            invoice.AccountingCustomerParty = accountingCustomerParty;

            // Add delivery information
            invoice.Delivery = new Delivery
            {
                DeliveryParty = new DeliveryParty
                {
                    PartyIdentifications = new List<PartyIdentification>
                    {
                        new PartyIdentification { ID = new IDType { SchemeID = "TIN", Value = transaction.Customer?.TinNo ?? "Recipient's TIN" } },
                        new PartyIdentification { ID = new IDType { SchemeID = "BRN", Value = transaction.Customer?.IdentityNo ?? "Recipient's BRN" } }
                    },
                    PostalAddress = new PostalAddress
                    {
                        CityName = transaction.ShippingAddress?.Region?.Name,
                        PostalZone = transaction.ShippingAddress?.PostalCode,
                        CountrySubentityCode = transaction.ShippingAddress?.State?.Abbreviation ?? "14", // Default to Kuala Lumpur
                        AddressLines = new List<AddressLine>
                        {
                            new AddressLine { Line = transaction.ShippingAddress?.Address1 },
                            new AddressLine { Line = transaction.ShippingAddress?.Address2 },
                            new AddressLine { Line = transaction.ShippingAddress?.Address3 }
                        },
                        Country = new Models.UBLInvoice.Country
                        {
                            IdentificationCode = new IdentificationCodeType
                            {
                                ListID = "ISO3166-1",
                                ListAgencyID = "6",
                                Value = "MYS"
                            }
                        }
                    },
                    PartyLegalEntity = new PartyLegalEntity
                    {
                        RegistrationName = transaction.Customer?.Name ?? "Recipient's Name"
                    }
                },
                Shipment = new Shipment
                {
                    ID = transaction.TrxNo,
                    FreightAllowanceCharge = new FreightAllowanceCharge
                    {
                        ChargeIndicator = "true",
                        AllowanceChargeReason = "Service charge",
                        Amount = new AmountType
                        {
                            CurrencyID = "MYR",
                            Value = "100.00" // Default value since DeliveryFee is not available
                        }
                    }
                }
            };

            // Add payment means
            invoice.PaymentMeans = new PaymentMeans
            {
                PaymentMeansCode = "01", // Cash payment
                PayeeFinancialAccount = new PayeeFinancialAccount
                {
                    ID = "**********" // Default bank account number
                }
            };

            // Add payment terms
            invoice.PaymentTerms = new PaymentTerms
            {
                Note = $"Payment method is cash" // Default payment method
            };

            // Add prepaid payment
            invoice.PrepaidPayment = new PrepaidPayment
            {
                ID = "E12345678912",
                PaidAmount = new AmountType
                {
                    CurrencyID = "MYR",
                    Value = "1.00"
                },
                PaidDate = transaction.TrxDatetime.AddDays(-1).ToString("yyyy-MM-dd"),
                PaidTime = transaction.TrxDatetime.AddDays(-1).ToString("HH:mm:ss'Z'")
            };

            // Add allowance charges at document level
            invoice.AllowanceCharges = new List<AllowanceCharge>
            {
                new AllowanceCharge
                {
                    ChargeIndicator = "false", // false = allowance (discount)
                    AllowanceChargeReason = "Discount",
                    Amount = new AmountType
                    {
                        CurrencyID = "MYR",
                        Value = transaction.TotalDiscount.ToString("F2")
                    }
                }
            };

            // Add service charge
            invoice.AllowanceCharges.Add(new AllowanceCharge
            {
                ChargeIndicator = "true", // true = charge
                AllowanceChargeReason = "Service charge",
                Amount = new AmountType
                {
                    CurrencyID = "MYR",
                    Value = "100.00" // Default service charge amount
                }
            });

            // Add invoice lines (from transaction items)
            invoice.InvoiceLines = new List<InvoiceLine>();
            int lineId = 1;
            foreach (var item in transaction.TransactionItem)
            {
                var invoiceLine = new InvoiceLine
                {
                    ID = lineId.ToString(),
                    InvoicedQuantity = new QuantityType
                    {
                        UnitCode = "C62", // Unit, piece, or item
                        Value = item.Quantity.ToString()
                    },
                    LineExtensionAmount = new AmountType
                    {
                        CurrencyID = "MYR",
                        Value = item.SubTotalAmount.ToString("F2")
                    },
                    Item = new Item
                    {
                        Name = item.Product?.Name,
                        Description = item.Product?.Description,
                        SellersItemIdentification = new SellersItemIdentification
                        {
                            ID = item.Product?.Code
                        },
                        OriginCountry = new OriginCountry
                        {
                            IdentificationCode = "MYS"
                        },
                        CommodityClassifications = new List<CommodityClassification>
                        {
                            new CommodityClassification
                            {
                                ItemClassificationCode = new ItemClassificationCodeType
                                {
                                    ListID = "PTC",
                                    Value = "9800.00.0010" // Default HS code
                                }
                            },
                            new CommodityClassification
                            {
                                ItemClassificationCode = new ItemClassificationCodeType
                                {
                                    ListID = "CLASS",
                                    Value = "003" // Default category code
                                }
                            }
                        }
                    },
                    Price = new Price
                    {
                        PriceAmount = new AmountType
                        {
                            CurrencyID = "MYR",
                            Value = item.UnitAmount.ToString("F2")
                        }
                    },
                    ItemPriceExtension = new ItemPriceExtension
                    {
                        Amount = new AmountType
                        {
                            CurrencyID = "MYR",
                            Value = (item.Quantity * item.UnitAmount).ToString("F2")
                        }
                    }
                };

                // Add line level allowance charges
                invoiceLine.AllowanceCharges = new List<AllowanceCharge>
                {
                    new AllowanceCharge
                    {
                        ChargeIndicator = "false", // false = allowance (discount)
                        AllowanceChargeReason = "Sample Description",
                        MultiplierFactorNumeric = "0.15",
                        Amount = new AmountType
                        {
                            CurrencyID = "MYR",
                            Value = "100.00"
                        }
                    }
                };

                // Add tax information if applicable
                if (item.SalesTaxAmount > 0)
                {
                    invoiceLine.TaxTotal = new TaxTotal
                    {
                        TaxAmount = new AmountType
                        {
                            CurrencyID = "MYR",
                            Value = item.SalesTaxAmount.ToString("F2")
                        },
                        TaxSubtotals = new List<TaxSubtotal>
                        {
                            new TaxSubtotal
                            {
                                TaxableAmount = new AmountType
                                {
                                    CurrencyID = "MYR",
                                    Value = item.ExclTaxAmount.ToString("F2")
                                },
                                TaxAmount = new AmountType
                                {
                                    CurrencyID = "MYR",
                                    Value = item.SalesTaxAmount.ToString("F2")
                                },
                                Percent = item.SalesTaxRate.ToString("F2"),
                                TaxCategory = new TaxCategory
                                {
                                    ID = item.SalesTaxNo?.Code ?? "E",
                                    Percent = item.SalesTaxRate.ToString("F2"),
                                    TaxScheme = new TaxScheme
                                    {
                                        ID = new TaxSchemeIDType
                                        {
                                            SchemeID = "UN/ECE 5153",
                                            SchemeAgencyID = "6",
                                            Value = "OTH"
                                        }
                                    }
                                }
                            }
                        }
                    };
                }
                else
                {
                    // Add exempt tax information
                    invoiceLine.TaxTotal = new TaxTotal
                    {
                        TaxAmount = new AmountType
                        {
                            CurrencyID = "MYR",
                            Value = "0"
                        },
                        TaxSubtotals = new List<TaxSubtotal>
                        {
                            new TaxSubtotal
                            {
                                TaxableAmount = new AmountType
                                {
                                    CurrencyID = "MYR",
                                    Value = item.ExclTaxAmount.ToString("F2")
                                },
                                TaxAmount = new AmountType
                                {
                                    CurrencyID = "MYR",
                                    Value = "0"
                                },
                                Percent = "0.00",
                                TaxCategory = new TaxCategory
                                {
                                    ID = "E",
                                    TaxExemptionReason = "Exempt New Means of Transport",
                                    TaxScheme = new TaxScheme
                                    {
                                        ID = new TaxSchemeIDType
                                        {
                                            SchemeID = "UN/ECE 5153",
                                            SchemeAgencyID = "6",
                                            Value = "OTH"
                                        }
                                    }
                                }
                            }
                        }
                    };
                }

                invoice.InvoiceLines.Add(invoiceLine);
                lineId++;
            }

            // Add tax totals
            invoice.TaxTotal = new TaxTotal
            {
                TaxAmount = new AmountType
                {
                    CurrencyID = "MYR",
                    Value = transaction.TotalSalesTaxAmount.ToString("F2")
                },
                TaxSubtotals = new List<TaxSubtotal>
                {
                    new TaxSubtotal
                    {
                        TaxableAmount = new AmountType
                        {
                            CurrencyID = "MYR",
                            Value = transaction.TotalExclTaxAmount.ToString("F2")
                        },
                        TaxAmount = new AmountType
                        {
                            CurrencyID = "MYR",
                            Value = transaction.TotalSalesTaxAmount.ToString("F2")
                        },
                        TaxCategory = new TaxCategory
                        {
                            ID = "01",
                            TaxScheme = new TaxScheme
                            {
                                ID = new TaxSchemeIDType
                                {
                                    SchemeID = "UN/ECE 5153",
                                    SchemeAgencyID = "6",
                                    Value = "OTH"
                                }
                            }
                        }
                    }
                }
            };

            // Add legal monetary total
            invoice.LegalMonetaryTotal = new LegalMonetaryTotal
            {
                LineExtensionAmount = new AmountType
                {
                    CurrencyID = "MYR",
                    Value = transaction.TotalAmount.ToString("F2")
                },
                TaxExclusiveAmount = new AmountType
                {
                    CurrencyID = "MYR",
                    Value = transaction.TotalExclTaxAmount.ToString("F2")
                },
                TaxInclusiveAmount = new AmountType
                {
                    CurrencyID = "MYR",
                    Value = transaction.TotalInclTaxAmount.ToString("F2")
                },
                AllowanceTotalAmount = new AmountType
                {
                    CurrencyID = "MYR",
                    Value = transaction.TotalDiscount.ToString("F2")
                },
                ChargeTotalAmount = new AmountType
                {
                    CurrencyID = "MYR",
                    Value = transaction.TotalServiceTaxAmount.ToString("F2")
                },
                PayableRoundingAmount = new AmountType
                {
                    CurrencyID = "MYR",
                    Value = transaction.TotalRoundingAdjustmentAmount.ToString("F2")
                },
                PayableAmount = new AmountType
                {
                    CurrencyID = "MYR",
                    Value = transaction.TotalPayableAmount.ToString("F2")
                }
            };

            // Serialize to XML
            var xmlString = SerializeToXml(invoice);
            return xmlString;
        }

        /// <summary>
        /// Serializes an object to XML string
        /// </summary>
        /// <typeparam name="T">Type of object to serialize</typeparam>
        /// <param name="obj">Object to serialize</param>
        /// <returns>XML string</returns>
        public static string SerializeToXml<T>(T obj)
        {
            var serializer = new XmlSerializer(typeof(T));
            var namespaces = new XmlSerializerNamespaces();

            // Add required namespaces for UBL
            namespaces.Add("", "urn:oasis:names:specification:ubl:schema:xsd:Invoice-2");
            namespaces.Add("cac", "urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2");
            namespaces.Add("cbc", "urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2");
            namespaces.Add("ext", "urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2");
            namespaces.Add("sig", "urn:oasis:names:specification:ubl:schema:xsd:CommonSignatureComponents-2");
            namespaces.Add("sac", "urn:oasis:names:specification:ubl:schema:xsd:SignatureAggregateComponents-2");
            namespaces.Add("sbc", "urn:oasis:names:specification:ubl:schema:xsd:SignatureBasicComponents-2");
            namespaces.Add("ds", "http://www.w3.org/2000/09/xmldsig#");
            namespaces.Add("xades", "http://uri.etsi.org/01903/v1.3.2#");

            using (var stringWriter = new StringWriter())
            {
                using (var xmlWriter = XmlWriter.Create(stringWriter, new XmlWriterSettings { Indent = true }))
                {
                    serializer.Serialize(xmlWriter, obj, namespaces);
                    return stringWriter.ToString();
                }
            }
        }

        /// <summary>
        /// Canonicalizes an XML document to ensure consistent hashing
        /// </summary>
        /// <param name="xmlDocument">XML document as string</param>
        /// <returns>Canonicalized XML document</returns>
        public static string CanonicalizeXml(string xmlDocument)
        {
            try
            {
                // Load the XML document
                var doc = new XmlDocument();
                doc.PreserveWhitespace = false;
                doc.LoadXml(xmlDocument);

                // Create a canonicalization transform
                var transform = new System.Security.Cryptography.Xml.XmlDsigC14NTransform();
                transform.LoadInput(doc);

                // Get the canonicalized output
                using (var ms = (System.IO.MemoryStream)transform.GetOutput())
                using (var sr = new System.IO.StreamReader(ms))
                {
                    return sr.ReadToEnd();
                }
            }
            catch (Exception)
            {
                // If canonicalization fails, return the original document with normalized line endings
                return xmlDocument.Replace("\r\n", "\n").Trim();
            }
        }

        /// <summary>
        /// Calculates SHA-256 hash of a string according to LHDN requirements
        /// </summary>
        /// <param name="input">Input string (XML document)</param>
        /// <returns>Hexadecimal string representation of the hash</returns>
        public static string CalculateHash(string input)
        {
            // Canonicalize the XML document to ensure consistent hashing
            string canonicalizedXml = CanonicalizeXml(input);

            using (var sha256 = SHA256.Create())
            {
                var bytes = Encoding.UTF8.GetBytes(canonicalizedXml);
                var hash = sha256.ComputeHash(bytes);

                // Convert to lowercase hexadecimal string as per LHDN requirements
                return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
            }
        }

        /// <summary>
        /// Converts XML string to Base64
        /// </summary>
        /// <param name="xml">XML string</param>
        /// <returns>Base64 encoded XML</returns>
        public static string XmlToBase64(string xml)
        {
            var bytes = Encoding.UTF8.GetBytes(xml);
            return Convert.ToBase64String(bytes);
        }

        /// <summary>
        /// Validates that the document hash matches the expected format for LHDN
        /// </summary>
        /// <param name="hash">Hash to validate</param>
        /// <returns>True if the hash is valid, false otherwise</returns>
        public static bool ValidateDocumentHash(string hash)
        {
            // LHDN requires a 64-character lowercase hexadecimal hash (SHA-256)
            if (string.IsNullOrEmpty(hash) || hash.Length != 64)
            {
                return false;
            }

            // Check if the hash contains only hexadecimal characters
            return hash.All(c => (c >= '0' && c <= '9') || (c >= 'a' && c <= 'f'));
        }
    }
}
