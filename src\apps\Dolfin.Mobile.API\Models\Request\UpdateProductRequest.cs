﻿using System.Text.Json.Serialization;

namespace Dolfin.Mobile.API.Models.Request
{
    public class UpdateProductRequest
    {
        [JsonIgnore]
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public Guid? CustomSalesTaxNo { get; set; }
        public Guid? CustomServiceTaxNo { get; set; }
        public decimal? Weight { get; set; }
        public decimal? Length { get; set; }
        public decimal? Width { get; set; }
        public decimal? Height { get; set; }
        public DateTime? AvailableStartAt { get; set; }
        public DateTime? AvailableEndAt { get; set; }
        public Guid? ClassificationId { get; set; }
        public int? DisplayOrder { get; set; }
        public bool? Published { get; set; }
        public Guid? CompanyId { get; set; } // only admin required
        public Guid? ProductCategoryId { get; set; }
    }
}
