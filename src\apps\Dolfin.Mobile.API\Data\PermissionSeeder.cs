﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Services;
using Dolfin.Utility.Enum;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Security.Claims;
using System.Threading.Tasks;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Data
{
    /// <summary>
    /// Seeds permissions into the database
    /// </summary>
    public class PermissionSeeder
    {
        private readonly RoleManager<ApplicationRole> _roleManager;
        private readonly ILogger<PermissionSeeder> _logger;
        private readonly IPermissionService _permissionService;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="roleManager">The role manager</param>
        /// <param name="permissionService">The permission service</param>
        /// <param name="logger">The logger</param>
        public PermissionSeeder(
            RoleManager<ApplicationRole> roleManager,
            IPermissionService permissionService,
            ILogger<PermissionSeeder> logger)
        {
            _roleManager = roleManager;
            _permissionService = permissionService;
            _logger = logger;
        }

        /// <summary>
        /// Seeds all permissions into the database
        /// </summary>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task SeedPermissionsAsync()
        {
            try
            {
                _logger.LogInformation("Starting permission seeding");

                // Get all permission constants from the Permissions class
                var permissions = GetAllPermissions();
                _logger.LogInformation("Found {PermissionCount} permissions to seed", permissions.Count);

                // Get all roles from the database
                var roles = await _roleManager.Roles.ToListAsync();
                _logger.LogInformation("Found {RoleCount} roles in the database", roles.Count);

                // Assign permissions to roles
                await AssignPermissionsToRolesAsync(permissions, roles);

                _logger.LogInformation("Permission seeding completed successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error seeding permissions");
                throw;
            }
        }

        /// <summary>
        /// Gets all permission constants from the Permissions class
        /// </summary>
        /// <returns>A list of permission constants</returns>
        private List<string> GetAllPermissions()
        {
            var permissions = new List<string>();

            // Get all nested classes in the Permissions class
            var permissionClasses = typeof(Permissions).GetNestedTypes();
            foreach (var permissionClass in permissionClasses)
            {
                // Get all constant fields in the nested class
                var fields = permissionClass.GetFields(BindingFlags.Public | BindingFlags.Static | BindingFlags.FlattenHierarchy);
                foreach (var field in fields)
                {
                    if (field.IsLiteral && !field.IsInitOnly)
                    {
                        var value = field.GetValue(null) as string;
                        if (!string.IsNullOrEmpty(value))
                        {
                            permissions.Add(value);
                        }
                    }
                }
            }

            return permissions;
        }

        /// <summary>
        /// Assigns permissions to roles based on role type
        /// </summary>
        /// <param name="permissions">The list of permissions</param>
        /// <param name="roles">The list of roles</param>
        /// <returns>A task representing the asynchronous operation</returns>
        private async Task AssignPermissionsToRolesAsync(List<string> permissions, List<ApplicationRole> roles)
        {
            // Get admin and super admin roles
            var adminRole = roles.FirstOrDefault(r => r.Name == UserTypeEnum.Admin.GetDescription());
            var superAdminRole = roles.FirstOrDefault(r => r.Name == UserTypeEnum.SuperAdmin.GetDescription());
            var superUserRole = roles.FirstOrDefault(r => r.Name == UserTypeEnum.SuperUser.GetDescription());
            var userRole = roles.FirstOrDefault(r => r.Name == UserTypeEnum.User.GetDescription());

            if (adminRole != null)
            {
                await AssignAllPermissionsToRoleAsync(adminRole.Id, permissions);
            }

            if (superAdminRole != null)
            {
                await AssignAllPermissionsToRoleAsync(superAdminRole.Id, permissions);
            }

            if (superUserRole != null)
            {
                await AssignLimitedPermissionsToRoleAsync(superUserRole.Id, permissions);
            }

            if (userRole != null)
            {
                await AssignBasicPermissionsToRoleAsync(userRole.Id, permissions);
            }
        }

        /// <summary>
        /// Assigns all permissions to a role
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <param name="permissions">The list of permissions</param>
        /// <returns>A task representing the asynchronous operation</returns>
        private async Task AssignAllPermissionsToRoleAsync(string roleId, List<string> permissions)
        {
            foreach (var permission in permissions)
            {
                await AddPermissionToRoleAsync(roleId, permission);
            }
        }

        /// <summary>
        /// Assigns limited permissions to a role (view and some create/update)
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <param name="permissions">The list of permissions</param>
        /// <returns>A task representing the asynchronous operation</returns>
        private async Task AssignLimitedPermissionsToRoleAsync(string roleId, List<string> permissions)
        {
            foreach (var permission in permissions)
            {
                // Allow all view permissions
                if (permission.Contains("View"))
                {
                    await AddPermissionToRoleAsync(roleId, permission);
                }
                // Allow create and update for non-admin areas
                else if ((permission.Contains("Create") || permission.Contains("Update")) &&
                        !permission.Contains("Admin"))
                {
                    await AddPermissionToRoleAsync(roleId, permission);
                }
            }
        }

        /// <summary>
        /// Assigns basic permissions to a role (view only)
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <param name="permissions">The list of permissions</param>
        /// <returns>A task representing the asynchronous operation</returns>
        private async Task AssignBasicPermissionsToRoleAsync(string roleId, List<string> permissions)
        {
            foreach (var permission in permissions)
            {
                // Allow only view permissions
                if (permission.Contains("View"))
                {
                    await AddPermissionToRoleAsync(roleId, permission);
                }
            }
        }

        /// <summary>
        /// Adds a permission to a role if it doesn't already exist
        /// </summary>
        /// <param name="roleId">The role ID</param>
        /// <param name="permission">The permission code</param>
        /// <returns>A task representing the asynchronous operation</returns>
        private async Task AddPermissionToRoleAsync(string roleId, string permission)
        {
            // Use the permission service to add the permission to the role
            var result = await _permissionService.AddPermissionToRoleAsync(roleId, permission);
            if (result)
            {
                _logger.LogInformation("Added permission {Permission} to role {RoleId}", permission, roleId);
            }
            else
            {
                _logger.LogWarning("Failed to add permission {Permission} to role {RoleId}", permission, roleId);
            }
        }
    }
}
