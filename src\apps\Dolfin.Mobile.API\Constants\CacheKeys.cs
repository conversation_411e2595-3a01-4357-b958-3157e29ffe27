using System;

namespace Dolfin.Mobile.API.Constants
{
    /// <summary>
    /// Contains all cache key definitions used throughout the application.
    /// Use this class to maintain consistent cache keys across different services.
    /// </summary>
    public static class CacheKeys
    {
        /// <summary>
        /// User-related cache keys
        /// </summary>
        public static class User
        {
            /// <summary>
            /// Format: CurrentUser_{userId}
            /// Used to cache the current user object
            /// </summary>
            public const string CurrentUserFormat = "CurrentUser_{0}";

            /// <summary>
            /// Format: UsersByBranch_{branchId}
            /// Used to cache users for a specific branch
            /// </summary>
            public const string UsersByBranchFormat = "UsersByBranch_{0}";

            /// <summary>
            /// Get the cache key for a specific user
            /// </summary>
            /// <param name="userId">The user ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetCurrentUserKey(string userId) => string.Format(CurrentUserFormat, userId);

            /// <summary>
            /// Get the cache key for users in a specific branch
            /// </summary>
            /// <param name="branchId">The branch ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetUsersByBranchKey(Guid branchId) => string.Format(UsersByBranchFormat, branchId);
        }

        /// <summary>
        /// Company-related cache keys
        /// </summary>
        public static class Company
        {
            /// <summary>
            /// Format: Company_{companyId}
            /// Used to cache company information
            /// </summary>
            public const string CompanyFormat = "Company_{0}";

            /// <summary>
            /// Get the cache key for a specific company
            /// </summary>
            /// <param name="companyId">The company ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetCompanyKey(Guid companyId) => string.Format(CompanyFormat, companyId);
        }

        /// <summary>
        /// Product-related cache keys
        /// </summary>
        public static class Product
        {
            /// <summary>
            /// Format: Product_{productId}
            /// Used to cache product information
            /// </summary>
            public const string ProductFormat = "Product_{0}";

            /// <summary>
            /// Format: ProductList_Company_{companyId}
            /// Used to cache product lists for a specific company
            /// </summary>
            public const string ProductListFormat = "ProductList_Company_{0}";

            /// <summary>
            /// Get the cache key for a specific product
            /// </summary>
            /// <param name="productId">The product ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetProductKey(Guid productId) => string.Format(ProductFormat, productId);

            /// <summary>
            /// Get the cache key for a company's product list
            /// </summary>
            /// <param name="companyId">The company ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetProductListKey(Guid companyId) => string.Format(ProductListFormat, companyId);
        }

        /// <summary>
        /// Customer-related cache keys
        /// </summary>
        public static class Customer
        {
            /// <summary>
            /// Format: Customer_{customerId}
            /// Used to cache customer information
            /// </summary>
            public const string CustomerFormat = "Customer_{0}";

            /// <summary>
            /// Format: CustomerList_Company_{companyId}
            /// Used to cache customer lists for a specific company
            /// </summary>
            public const string CustomerListFormat = "CustomerList_Company_{0}";

            /// <summary>
            /// Get the cache key for a specific customer
            /// </summary>
            /// <param name="customerId">The customer ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetCustomerKey(Guid customerId) => string.Format(CustomerFormat, customerId);

            /// <summary>
            /// Get the cache key for a company's customer list
            /// </summary>
            /// <param name="companyId">The company ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetCustomerListKey(Guid companyId) => string.Format(CustomerListFormat, companyId);
        }

        /// <summary>
        /// Lookup-related cache keys
        /// </summary>
        public static class Lookup
        {
            /// <summary>
            /// Format: LookupGroup_{lookupGroupId}
            /// Used to cache lookup group information
            /// </summary>
            public const string LookupGroupFormat = "LookupGroup_{0}";

            /// <summary>
            /// Format: LookupGroupByCode_{code}
            /// Used to cache lookup group by code
            /// </summary>
            public const string LookupGroupByCodeFormat = "LookupGroupByCode_{0}";

            /// <summary>
            /// Format: LookupGroupList
            /// Used to cache all lookup groups
            /// </summary>
            public const string LookupGroupListFormat = "LookupGroupList";

            /// <summary>
            /// Format: LookupGroupList_Company_{companyId}
            /// Used to cache lookup groups for a specific company
            /// </summary>
            public const string LookupGroupListByCompanyFormat = "LookupGroupList_Company_{0}";

            /// <summary>
            /// Format: LookupItem_{lookupItemId}
            /// Used to cache lookup item information
            /// </summary>
            public const string LookupItemFormat = "LookupItem_{0}";

            /// <summary>
            /// Format: LookupItemList_Group_{lookupGroupId}
            /// Used to cache lookup items for a specific group
            /// </summary>
            public const string LookupItemListByGroupFormat = "LookupItemList_Group_{0}";

            /// <summary>
            /// Get the cache key for a specific lookup group
            /// </summary>
            /// <param name="lookupGroupId">The lookup group ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetLookupGroupKey(Guid lookupGroupId) => string.Format(LookupGroupFormat, lookupGroupId);

            /// <summary>
            /// Get the cache key for a lookup group by code
            /// </summary>
            /// <param name="code">The lookup group code</param>
            /// <returns>The formatted cache key</returns>
            public static string GetLookupGroupByCodeKey(string code) => string.Format(LookupGroupByCodeFormat, code);

            /// <summary>
            /// Get the cache key for all lookup groups
            /// </summary>
            /// <returns>The formatted cache key</returns>
            public static string GetLookupGroupListKey() => LookupGroupListFormat;

            /// <summary>
            /// Get the cache key for lookup groups in a specific company
            /// </summary>
            /// <param name="companyId">The company ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetLookupGroupListByCompanyKey(Guid companyId) => string.Format(LookupGroupListByCompanyFormat, companyId);

            /// <summary>
            /// Get the cache key for a specific lookup item
            /// </summary>
            /// <param name="lookupItemId">The lookup item ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetLookupItemKey(Guid lookupItemId) => string.Format(LookupItemFormat, lookupItemId);

            /// <summary>
            /// Get the cache key for lookup items in a specific group
            /// </summary>
            /// <param name="lookupGroupId">The lookup group ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetLookupItemListByGroupKey(Guid lookupGroupId) => string.Format(LookupItemListByGroupFormat, lookupGroupId);
        }

        /// <summary>
        /// Permission-related cache keys
        /// </summary>
        public static class Permission
        {
            /// <summary>
            /// Format: RolePermissions_{roleId}
            /// Used to cache permissions for a specific role
            /// </summary>
            public const string RolePermissionsFormat = "RolePermissions_{0}";

            /// <summary>
            /// Format: RolePermissions_Multiple_{roleIds}
            /// Used to cache permissions for multiple roles
            /// </summary>
            public const string MultipleRolePermissionsFormat = "RolePermissions_Multiple_{0}";

            /// <summary>
            /// Format: RoleHasPermission_{roleId}_{permissionCode}
            /// Used to cache whether a role has a specific permission
            /// </summary>
            public const string RoleHasPermissionFormat = "RoleHasPermission_{0}_{1}";

            /// <summary>
            /// Format: UserPermissions_{userId}
            /// Used to cache permissions for a specific user
            /// </summary>
            public const string UserPermissionsFormat = "UserPermissions_{0}";

            /// <summary>
            /// Get the cache key for permissions of a specific role
            /// </summary>
            /// <param name="roleId">The role ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetRolePermissionsKey(Guid roleId) => string.Format(RolePermissionsFormat, roleId);

            /// <summary>
            /// Get the cache key for permissions of multiple roles
            /// </summary>
            /// <param name="roleIds">The role IDs</param>
            /// <returns>The formatted cache key</returns>
            public static string GetMultipleRolePermissionsKey(IEnumerable<Guid> roleIds) =>
                string.Format(MultipleRolePermissionsFormat, string.Join("_", roleIds));

            /// <summary>
            /// Get the cache key for checking if a role has a specific permission
            /// </summary>
            /// <param name="roleId">The role ID</param>
            /// <param name="permissionCode">The permission code</param>
            /// <returns>The formatted cache key</returns>
            public static string GetRoleHasPermissionKey(Guid roleId, string permissionCode) =>
                string.Format(RoleHasPermissionFormat, roleId, permissionCode);

            /// <summary>
            /// Get the cache key for permissions of a specific user
            /// </summary>
            /// <param name="userId">The user ID</param>
            /// <returns>The formatted cache key</returns>
            public static string GetUserPermissionsKey(string userId) => string.Format(UserPermissionsFormat, userId);
        }
    }
}
