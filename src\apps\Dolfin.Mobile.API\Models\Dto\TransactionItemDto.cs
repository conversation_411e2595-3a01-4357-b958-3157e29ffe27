﻿using Dolfin.Framework.Data.Domains;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class TransactionItemDto : _BaseDomain
    {
        public TransactionItemDto()
        {
            //InventoryItemTransactionItem = new HashSet<InventoryItemTransactionItem>();
        }
        public Guid Id { get; set; }
        public string ProductUOMPrimaryMCode { get; set; }
        public string ProductUOMSecondaryMCode { get; set; }
        public string ClassificationCode { get; set; }
        public decimal FractionTotal { get; set; }
        public decimal FractionQuantity { get; set; }
        public decimal Quantity { get; set; }
        public decimal Discount { get; set; }
        public decimal TotalSalesTaxNo { get; set; }
        public decimal TotalServiceTaxNo { get; set; }
        public Guid SalesTaxNoId { get; set; }
        public Guid ServiceTaxNoId { get; set; }
        public int OrderGroup { get; set; }
        public decimal ExclTax { get; set; }
        public decimal InclTax { get; set; }
        public decimal TaxAmount { get; set; }
        public decimal OriUnitProductCost { get; set; }
        public decimal UnitAmount { get; set; }
        public decimal SubTotalAmount { get; set; }
        public decimal AdjAmount { get; set; }
        public Guid ProductPriceId { get; set; }
        public Guid CustomerId { get; set; }
        public Guid ProductUOMId { get; set; }
        public Guid TrxId { get; set; }
        public Guid ProductId { get; set; }
        public Guid ClassificationId { get; set; }
        public Guid TransactionItemStatusId { get; set; }
        public virtual TaxRateDto? SalesTaxNo { get; set; }
        public virtual TaxRateDto? ServiceTaxNo { get; set; }
        //public virtual ProductPriceDto? ProductPrice { get; set; }
        //public Customer Customer { get; set; }
        public virtual ProductUOMDto? ProductUOM { get; set; }
        //public virtual Transaction? Transaction { get; set; }
        public virtual ProductDto? Product { get; set; }
        public virtual ClassificationDto? Classification { get; set; }
        public virtual TransactionItemStatusDto? TransactionItemStatus { get; set; }
        //public virtual ICollection<InventoryItemTransactionItem> InventoryItemTransactionItem { get; }
    }
}
