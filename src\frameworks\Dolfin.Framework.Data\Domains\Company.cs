﻿using DocumentFormat.OpenXml.Office2019.Drawing.Model3D;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Company : _BaseDomain
    {
        public Company()
        {
            Branch = new HashSet<Branch>();
            Product = new HashSet<Product>();
            ProductCategory = new HashSet<ProductCategory>();
            //Role = new HashSet<Role>();
            //Transaction = new HashSet<Transaction>();
            User = new HashSet<ApplicationUser>();
            Cust = new HashSet<Customer>();
        }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public required string RegNo { get; set; }
        public required string SstNo { get; set; }
        public Guid? DefaultSalesTaxNoId { get; set; }
        public Guid? DefaultServiceTaxNoId { get; set; }
        public required string TinNo { get; set; }
        public string? Logo { get; set; }
        public bool IsBranchSameProduct { get; set; }
        public bool IsEnableInventory { get; set; }
        public bool IsRoundingAdjustment { get; set; }
        public bool? IsEInvoiceSubmitAuto { get; set; }
        public int? ConsolidatePay { get; set; }
        public decimal? ServiceCharge { get; set; }
        public DateTime ExpiredAt { get; set; }
        public string? WebSiteUrl { get; set; }
        public string? EInvoiceClientId { get; set; }
		public string? EInvoiceClientSecret { get; set; }
		public string? EInvoiceToken { get; set; }
        public DateTime? EInvoiceTokenDateTime { get; set; }
        public Guid CurrencyId { get; set; }
        public Guid? SubscriptionId { get; set; }
        public Guid? MsicId { get; set; }
        public required Guid TaxCategoryId { get; set; }
        public virtual TaxRate? DefaultSalesTaxNo { get; set; }
        public virtual TaxRate? DefaultServiceTaxNo { get; set; }
        public virtual Currency? Currency { get; set; }
        public virtual Subscription Subscription { get; set; }
        public virtual Msic? Msic { get; set; }
        public virtual TaxCategories TaxCategory { get; set; }
        public virtual ICollection<Branch> Branch { get; }
        public virtual ICollection<Product> Product { get; }
        public virtual ICollection<ProductCategory> ProductCategory { get; }
        //public virtual ICollection<Role> Role { get; }
        //public virtual ICollection<Transaction> Transaction { get; }
        public virtual ICollection<ApplicationUser> User { get; }
        public virtual ICollection<Customer> Cust { get; }
    }
}
