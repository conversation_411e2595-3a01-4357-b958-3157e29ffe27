﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Utility.Models;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Helper;
using Dolfin.Utility.Enum;
using Dolfin.Utility.Utils;
using static Dolfin.Utility.Enum.Enums;

namespace Dolfin.Mobile.API.Services
{
    public interface IProductService
    {
        // Method to get a database context
        DolfinDbContext GetDbContext();
        #region Product Categories
        Task<BaseResponse<PagedList<ProductCategory>>> GetProductCategoryList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null);
        Task<BaseResponse<ProductCategory>> GetProductCategoryByGuid(Guid productCategoryId);
        Task<BaseResponse<ResultId>> InsertProductCategory(ProductCategoryRequest reqBody);
        Task<BaseResponse<ResultId>> UpdateProductCategory(UpdateProductCategoryRequest reqBody);
        Task<NoResultResponse> DeleteProductCategory(Guid id);
        #endregion

        #region Product UOM
        Task<BaseResponse<PagedList<ProductUOM>>> GetProductUOMList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null);
        Task<BaseResponse<ProductUOM>> GetProductUOMByGuid(Guid productUOMId, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ProductUOM>> GetProductUOMByCode(string productUOMCode, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ProductUOM>> GetProductUOMByProductAndUomId(Guid productId, Guid uomId, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultId>> InsertProductUOM(ProductUOMRequest reqBody, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultId>> UpdateProductUOM(UpdateProductUOMRequest reqBody, DolfinDbContext dbContextRollback = null);
        Task<NoResultResponse> DeleteProductUOM(Guid id);
        #endregion

        #region Product
        Task<BaseResponse<PagedList<Product>>> GetProductList(Pagination pagination = null, CommonFilterList filterList = null, Guid? companyId = null);
        Task<BaseResponse<Product>> GetProductByGuid(Guid productId, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultId>> InsertProduct(ProductRequest reqBody, ApplicationUser currentUser, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultId>> UpdateProduct(UpdateProductRequest reqBody, ApplicationUser currentUser, DolfinDbContext dbContextRollback = null);
        Task<NoResultResponse> DeleteProduct(Guid id, DolfinDbContext dbContextRollback = null);
        #endregion

        #region Product Cost Method
        Task<BaseResponse<PagedList<ProductCostMethod>>> GetProductCostMethodList(Pagination pagination = null, CommonFilterList filterList = null);
        Task<BaseResponse<ProductCostMethod>> GetProductCostMethodByGuid(Guid productCostMethodId);
        Task<BaseResponse<ResultId>> InsertProductCostMethod(ProductCostMethodRequest reqBody);
        Task<BaseResponse<ResultId>> UpdateProductCostMethod(UpdateProductCostMethodRequest reqBody);
        Task<NoResultResponse> DeleteProductCostMethod(Guid id);
        #endregion

        #region UOM
        Task<BaseResponse<PagedList<UOM>>> GetUOMList(Pagination pagination = null, CommonFilterList filterList = null);
        Task<BaseResponse<UOM>> GetUOMByGuid(Guid uomId);
        Task<BaseResponse<ResultId>> InsertUOM(UOMRequest reqBody);
        Task<BaseResponse<ResultId>> UpdateUOM(UpdateUOMRequest reqBody);
        Task<NoResultResponse> DeleteUOM(Guid id);
        #endregion

        #region UOM Categories
        Task<BaseResponse<PagedList<UOMCategories>>> GetUOMCategoryList(Pagination pagination = null, CommonFilterList filterList = null);
        Task<BaseResponse<UOMCategories>> GetUOMCategoryByGuid(Guid uomCategoryId);
        #endregion

        #region Classification
        Task<BaseResponse<Classification>> GetClassificationByGuid(Guid classificationId);
        #endregion

        #region Product Price
        Task<BaseResponse<PagedList<ProductPrice>>> GetProductPriceList(Pagination pagination = null, CommonFilterList filterList = null, Guid? productUOMId = null);
        Task<BaseResponse<ProductPrice>> GetProductPriceByGuid(Guid productPriceId);
        Task<BaseResponse<ResultId>> InsertProductPrice(ProductPriceRequest reqBody, DolfinDbContext dbContextRollback = null);
        Task<BaseResponse<ResultId>> UpdateProductPrice(UpdateProductPriceRequest reqBody, DolfinDbContext dbContextRollback = null);
        Task<NoResultResponse> DeleteProductPrice(Guid id);
        #endregion
    }
}
