# Dolfin Mobile API Documentation

This document provides a comprehensive overview of the Dolfin Mobile API endpoints, request/response models, and authentication mechanisms.

## Table of Contents

1. [Authentication](#authentication)
2. [User Management](#user-management)
3. [Company Management](#company-management)
4. [Customer Management](#customer-management)
5. [Product Management](#product-management)
6. [Inventory Management](#inventory-management)
7. [Transaction Management](#transaction-management)
8. [E-Invoice Integration](#e-invoice-integration)
9. [Common Response Format](#common-response-format)

## Authentication

The API uses JWT (JSON Web Token) authentication with optional cookie-based authentication.

### Authentication Methods

#### JWT Token Authentication

The API primarily uses JWT tokens for authentication. When a user logs in, the API returns a JWT access token and a refresh token. The access token should be included in the `Authorization` header of subsequent requests using the Bearer scheme:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

Access tokens have a limited lifespan (typically 15-60 minutes). When an access token expires, the refresh token can be used to obtain a new access token without requiring the user to log in again.

#### Cookie-Based Authentication

The API also supports cookie-based authentication, which can be enabled or disabled via configuration. When enabled, the API sets the following cookies after successful login:

- `X-Access-Token` - Contains the JWT access token
- `X-Refresh-Token` - Contains the refresh token
- `Dolfin.Auth` - ASP.NET Core Identity authentication cookie

These cookies are automatically included in subsequent requests to the API, eliminating the need to manually set the `Authorization` header.

### Authentication Configuration

The authentication behavior can be configured in the `appsettings.json` file:

```json
{
  "JwtSettings": {
    "SecretKey": "your-secret-key",
    "Issuer": "dolfin-api",
    "Audience": "dolfin-clients",
    "ExpirationMinutes": 60,
    "UseCookieAuthentication": true
  }
}
```

Set `UseCookieAuthentication` to `true` to enable cookie-based authentication, or `false` to use only JWT token authentication.

### Endpoints

#### Register

```
POST /api/auth/register
```

Creates a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Password123!",
  "confirmPassword": "Password123!",
  "username": "johndoe",
  "fullName": "John Doe",
  "phoneNo1": "**********",
  "phoneNo2": "**********",
  "faxNo1": "**********",
  "faxNo2": "**********",
  "serialNo": "ABC123",
  "branchId": "********-0000-0000-0000-************",
  "companyId": "********-0000-0000-0000-************",
  "userTypeId": "********-0000-0000-0000-************"
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "displayMessage": "Registration successful. Please check your email for confirmation."
  }
}
```

#### Login

```
POST /api/auth/login
```

Authenticates a user and returns a JWT token.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "Password123!"
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "tokenId": "string",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "string",
    "expiresIn": 3600,
    "user": {
      "id": "********-0000-0000-0000-************",
      "username": "johndoe",
      "fullName": "John Doe",
      "email": "<EMAIL>",
      "phoneNo1": "**********",
      "emailConfirmed": true,
      "lastLoginAt": "2023-01-01T00:00:00Z",
      "branchId": "********-0000-0000-0000-************",
      "companyId": "********-0000-0000-0000-************"
    },
    "displayMessage": "Login successful"
  }
}
```

#### Logout

```
POST /api/auth/logout
```

Logs out the current user.

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "displayMessage": "Logout successful"
  }
}
```

#### Refresh Token

```
POST /api/auth/refresh-token
```

Refreshes an access token using a refresh token.

**Request Body:**
```json
{
  "token": "refresh_token_string"
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "tokenId": "string",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "string",
    "expiresIn": 3600,
    "displayMessage": "Token refreshed successfully"
  }
}
```

#### Revoke Token

```
POST /api/auth/revoke-token
```

Revokes a refresh token.

**Request Body:**
```json
{
  "token": "refresh_token_string",
  "reason": "User logged out"
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "displayMessage": "Token revoked successfully"
  }
}
```

#### Other Authentication Endpoints

- `GET /api/auth/confirm-email?userId={userId}&code={code}` - Confirms a user's email address
- `POST /api/auth/resend-confirmation-email` - Resends the email confirmation link
- `POST /api/auth/forgot-password` - Initiates the password reset process
- `POST /api/auth/reset-password` - Resets a user's password
- `POST /api/auth/change-password` - Changes the password for the authenticated user

## User Management

### Endpoints

#### Get User Profile

```
GET /api/user/profile?userId={userId}
```

Retrieves a user's profile information.

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "id": "********-0000-0000-0000-************",
    "username": "johndoe",
    "fullName": "John Doe",
    "email": "<EMAIL>",
    "phoneNo1": "**********",
    "emailConfirmed": true,
    "lastLoginAt": "2023-01-01T00:00:00Z",
    "branchId": "********-0000-0000-0000-************",
    "companyId": "********-0000-0000-0000-************"
  }
}
```

#### Delete User

```
POST /api/user/delete?userId={userId}
```

Deletes a user account.

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success"
}
```

## Company Management

### Endpoints

#### Get Company Profile

```
GET /api/company/profile?companyId={companyId}
```

Retrieves a company's profile information.

#### Get Company by User ID

```
GET /api/company/userid?userId={userId}
```

Retrieves a company based on a user ID.

#### Create Company

```
POST /api/company/create
```

Creates a new company.

#### Get Tax Categories

```
GET /api/company/taxcategory
```

Retrieves a list of tax categories.

#### Get Prefix List

```
GET /api/company/branch/prefix
```

Retrieves a list of prefixes for a branch.

#### Generate Code

```
GET /api/company/branch/prefix/generatecode?tableName={tableName}&branchId={branchId}
```

Generates a code for a specific table and branch.

## Customer Management

### Endpoints

#### Get Customer List

```
GET /api/customer
```

Retrieves a list of customers.

#### Get Customer by ID

```
GET /api/customer/get/{customerId}
```

Retrieves a customer by ID.

#### Create Customer

```
POST /api/customer/create
```

Creates a new customer.

#### Delete Customer

```
POST /api/customer/delete?customerId={customerId}
```

Deletes a customer.

## Product Management

### Endpoints

#### Get Product List

```
GET /api/product
```

Retrieves a list of products.

#### Get Product Categories

```
GET /api/product/productcategory
```

Retrieves a list of product categories.

#### Get UOM List

```
GET /api/product/uom
```

Retrieves a list of units of measurement.

#### Get UOM Categories

```
GET /api/product/uomcaterory/get
```

Retrieves a list of UOM categories.

## Inventory Management

### Endpoints

#### Get Inventory List

```
GET /api/inventory
```

Retrieves a list of inventories.

**Query Parameters:**
- `pagination` - Pagination parameters
- `commonFilter` - Common filter parameters
- `branchId` - Optional branch ID filter

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": [
    {
      "id": "********-0000-0000-0000-************",
      "code": "INV001",
      "name": "Main Warehouse",
      "branchId": "********-0000-0000-0000-************",
      "isActive": true,
      "createdAt": "2023-01-01T00:00:00Z",
      "createdBy": "********-0000-0000-0000-************"
    }
  ]
}
```

#### Get Inventory by ID

```
GET /api/inventory/get/{inventoryId}
```

Retrieves an inventory by ID.

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "id": "********-0000-0000-0000-************",
    "code": "INV001",
    "name": "Main Warehouse",
    "branchId": "********-0000-0000-0000-************",
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00Z",
    "createdBy": "********-0000-0000-0000-************",
    "branch": {
      "id": "********-0000-0000-0000-************",
      "name": "Main Branch",
      "companyId": "********-0000-0000-0000-************"
    }
  }
}
```

#### Create Inventory

```
POST /api/inventory/create
```

Creates a new inventory.

**Request Body:**
```json
{
  "code": "INV001",
  "name": "Main Warehouse",
  "branchId": "********-0000-0000-0000-************"
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "id": "********-0000-0000-0000-************"
  }
}
```

#### Update Inventory

```
POST /api/inventory/update
```

Updates an existing inventory.

**Request Body:**
```json
{
  "id": "********-0000-0000-0000-************",
  "code": "INV001",
  "name": "Updated Warehouse Name",
  "branchId": "********-0000-0000-0000-************"
}
```

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    "id": "********-0000-0000-0000-************"
  }
}
```

#### Delete Inventory

```
POST /api/inventory/delete?inventoryId={inventoryId}
```

Deletes an inventory.

**Response:**
```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success"
}
```

## Transaction Management

### Endpoints

#### Get Transaction

```
GET /api/transaction/transaction/get?transactionId={transactionId}
```

Retrieves a transaction by ID.

#### Create Transaction

```
POST /api/transaction/transaction/create?isCalculationRequired={isCalculationRequired}
```

Creates a new transaction.

#### Update Transaction

```
POST /api/transaction/transaction/update?isCalculationRequired={isCalculationRequired}
```

Updates an existing transaction.

## E-Invoice Integration

### Endpoints

#### Login to Taxpayer System

```
GET /api/einvoice/logintaxpayersystem
```

Logs in to the taxpayer system.

#### Search TIN

```
GET /api/einvoice/searchtin
```

Searches for a Tax Identification Number.

#### Validate TIN

```
GET /api/einvoice/validatetin
```

Validates a Tax Identification Number.

#### Get Document Types

```
GET /api/einvoice/getdocumenttypes
```

Retrieves a list of document types.

#### Submit Document

```
POST /api/einvoice/submitdocument
```

Submits a document to the e-invoice system.

#### Cancel Document

```
PUT /api/einvoice/canceldocument/{documentUUID}
```

Cancels a document in the e-invoice system.

#### Reject Document

```
PUT /api/einvoice/rejectdocument/{documentUUID}
```

Rejects a document in the e-invoice system.

## Common Response Format

All API endpoints return responses in a standardized format:

```json
{
  "isSuccessful": true,
  "statusCode": 200,
  "statusMessage": "Success",
  "result": {
    // Endpoint-specific result data
  }
}
```

For paginated responses, the API includes pagination metadata in the `X-Pagination` header:

```json
{
  "totalCount": 100,
  "pageSize": 10,
  "currentPage": 1,
  "totalPages": 10,
  "hasNext": true,
  "hasPrevious": false
}
```

Error responses follow the same format but include error details:

```json
{
  "isSuccessful": false,
  "statusCode": 400,
  "statusMessage": "Bad Request",
  "exception": "Error details (only in development mode)"
}
```
