﻿using Dolfin.Framework.Data.Domains;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Models.Dto
{
    public partial class ProductDto
    {
        public ProductDto()
        {
            //InventoryProduct = new HashSet<InventoryProduct>();
            //InventoryItem = new HashSet<InventoryItem>();
            ProductUOM = new HashSet<ProductUOMDto>();
            //TransactionItem = new HashSet<TransactionItem>();
        }
        public required Guid Id { get; set; }
        public required string Code { get; set; }
        public required string Name { get; set; }
        public string? Description { get; set; }
        public bool IsTaxExempt { get; set; }
        public bool IsTaxExcl { get; set; }
        public Guid? CustomSalesTaxNoId { get; set; }
        public Guid? CustomServiceTaxNoId { get; set; }
        public required string Sku { get; set; }
        public string? Image { get; set; }
        public decimal? Weight { get; set; }
        public decimal? Length { get; set; }
        public decimal? Width { get; set; }
        public decimal? Height { get; set; }
        public required string AccountCode { get; set; }
        public DateTime? AvailableStartAt { get; set; }
        public DateTime? AvailableEndAt { get; set; }
        public int? DisplayOrder { get; set; }
        public bool Published { get; set; }
        public Guid CompanyId { get; set; }
        public Guid ProductCategoryId { get; set; }
        public Guid ProductCostMethodId { get; set; }
        public Guid? ClassificationId { get; set; }
        public Guid CurrencyId { get; set; }
        public virtual CompanyDto Company { get; set; }
        public virtual ProductCategoryDto ProductCategory { get; set; }
        public virtual ProductCostMethodDto ProductCostMethod { get; set; }
        public virtual TaxRateDto CustomSalesTaxNo { get; set; }
        public virtual TaxRateDto CustomServiceTaxNo { get; set; }
        public virtual ClassificationDto Classification { get; set; }
        public virtual CurrencyDto Currency { get; set; }
        //public virtual ICollection<InventoryProduct> InventoryProduct { get; set; }
        //public virtual ICollection<InventoryItem> InventoryItem { get; }
        public virtual ICollection<ProductUOMDto> ProductUOM { get; set; }

        // Product images from file uploads
        public virtual ICollection<FileUploadDto> Images { get; set; } = new List<FileUploadDto>();
        //public virtual ICollection<TransactionItem> TransactionItem { get; }

    }
}
