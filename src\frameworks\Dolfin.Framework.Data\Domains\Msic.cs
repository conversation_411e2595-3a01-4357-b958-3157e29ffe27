﻿using Dolfin.Framework.Data.Domains.CustomIdentity;
using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Dolfin.Framework.Data.Domains
{
    public partial class Msic : _BaseDomain
    {
        public Msic()
        {
            Company = new HashSet<Company>();
        }
        public required string Code { get; set; }
        public required string Description { get; set; }
        public required Guid MsicCategoryReferenceId { get; set; }
        public MsicCategoryReference MsicCategoryReference { get; set; }

        public virtual ICollection<Company> Company { get; }
    }
}
