using Dolfin.Framework.Data.Domains.CustomIdentity;
using Dolfin.Framework.Repository.Interfaces;

namespace Dolfin.Mobile.API.Repositories.Interfaces
{
    /// <summary>
    /// Repository interface for ApplicationUser entity
    /// </summary>
    public interface IUserRepository : IRepository<ApplicationUser>
    {
        /// <summary>
        /// Get user by GUID
        /// </summary>
        /// <param name="id">User ID</param>
        /// <returns>User or null if not found</returns>
        Task<ApplicationUser?> GetUserByGuidAsync(string id);

        /// <summary>
        /// Get user by username
        /// </summary>
        /// <param name="username">Username</param>
        /// <returns>User or null if not found</returns>
        Task<ApplicationUser?> GetUserByUsernameAsync(string username);

        /// <summary>
        /// Get users by branch ID
        /// </summary>
        /// <param name="branchId">Branch ID</param>
        /// <returns>List of users for the branch</returns>
        Task<List<ApplicationUser>> GetUsersByBranchIdAsync(Guid branchId);

        /// <summary>
        /// Get list of users by user IDs
        /// </summary>
        /// <param name="userIds">Array of user IDs</param>
        /// <returns>List of users</returns>
        Task<List<ApplicationUser>> GetUserListAsync(string[] userIds);

        /// <summary>
        /// Invalidate user cache
        /// </summary>
        /// <param name="userId">User ID</param>
        void InvalidateUserCache(string userId);
    }
}
