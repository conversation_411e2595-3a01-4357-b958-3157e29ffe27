using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Repository.Interfaces;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories.Interfaces
{
    /// <summary>
    /// Repository interface for Customer entity
    /// </summary>
    public interface ICustomerRepository : IRepository<Customer>
    {
        /// <summary>
        /// Get customers by company ID
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>List of customers for the company</returns>
        Task<IEnumerable<Customer>> GetCustomersByCompanyAsync(Guid companyId);
        
        /// <summary>
        /// Get customer with all related entities
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        /// <returns>Customer with all related entities</returns>
        Task<Customer> GetCustomerWithDetailsAsync(Guid customerId);
        
        /// <summary>
        /// Invalidate customer cache
        /// </summary>
        /// <param name="customerId">Customer ID</param>
        void InvalidateCustomerCache(Guid customerId);
        
        /// <summary>
        /// Invalidate company customers cache
        /// </summary>
        /// <param name="companyId">Company ID</param>
        void InvalidateCompanyCustomersCache(Guid companyId);
    }
}
