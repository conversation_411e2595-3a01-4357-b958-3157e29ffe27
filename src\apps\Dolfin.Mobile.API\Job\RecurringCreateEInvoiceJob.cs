﻿﻿using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Mobile.API.Helper;
using Dolfin.Mobile.API.Models;
using Dolfin.Mobile.API.Models.Request;
using Dolfin.Mobile.API.Services;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using System;
using System.Threading;

namespace Dolfin.Mobile.API.Scheduler
{
    /// <summary>
    /// Recurring job to automatically create eInvoices for transactions based on company settings
    /// </summary>
    internal class RecurringCreateEInvoiceJob : BaseRecurringJob
    {
        private readonly RecurringCreateEInvoiceJobSetting _jobSetting;
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;

        public RecurringCreateEInvoiceJob(
            ILogger<RecurringCreateEInvoiceJob> logger,
            IOptions<RecurringCreateEInvoiceJobSetting> jobSetting,
            IServiceProvider serviceProvider)
        {
            _logger = logger;
            _jobSetting = jobSetting.Value;
            _serviceProvider = serviceProvider;
        }

        public override void Execute()
        {
            try
            {
                _logger.LogInformation("[{JobName}] Job Started at {StartTime}", this.Name, DateTime.Now);
                _logger.LogInformation("[{JobName}] Using cron expression: {CronExpression}", this.Name, _jobSetting.CronExpression);

                // Process transactions that need eInvoices
                ProcessTransactionsForEInvoice().Wait();

                _logger.LogInformation("[{JobName}] Job Completed at {EndTime}", this.Name, DateTime.Now);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[{JobName}] Error executing recurring job: {ErrorMessage}", this.Name, ex.Message);
                // Continue execution despite errors - don't rethrow
            }
        }

        private async Task ProcessTransactionsForEInvoice()
        {
            // Create a scope to resolve scoped services
            using var scope = _serviceProvider.CreateScope();
            var scopedProvider = scope.ServiceProvider;

            // Get required services from the scoped provider
            var dbContext = scopedProvider.GetRequiredService<DolfinDbContext>();
            var eInvoiceService = scopedProvider.GetRequiredService<IEinvoiceService>();

            try
            {
                // Get lookback date
                var lookbackDate = DateTime.UtcNow.AddDays(-_jobSetting.DaysLookback);

                // Get companies with auto eInvoice enabled
                var companies = await dbContext.Set<Company>()
                    .Where(c => c.IsEInvoiceSubmitAuto == true && c.IsActive)
                    .ToListAsync();

                _logger.LogInformation("Found {Count} companies with auto eInvoice enabled", companies.Count);

                foreach (var company in companies)
                {
                    try
                    {
                        // Get transactions for this company that don't have eInvoices yet
                        var transactions = await dbContext.Set<Transaction>()
                            .Include(t => t.Branch)
                            .Where(t => t.Branch.CompanyId == company.Id &&
                                       (t.IsEInvoiceCreated == null || t.IsEInvoiceCreated == false) &&
                                       t.IsActive &&
                                       t.TrxDatetime >= lookbackDate)
                            .OrderBy(t => t.TrxDatetime)
                            .Take(_jobSetting.BatchSize)
                            .ToListAsync();

                        _logger.LogInformation("Found {Count} transactions for company {CompanyName} that need eInvoices", transactions.Count, company.Name);

                        foreach (var transaction in transactions)
                        {
                            try
                            {
                                _logger.LogInformation("Creating eInvoice for transaction {TransactionNo}", transaction.TrxNo);

                                // Create eInvoice for this transaction
                                var submitRequest = new EInvoiceSubmitDocumentRequest
                                {
                                    TrxId = transaction.Id,
                                    // Use default document type and version
                                    DocumentType = null,
                                    DocumentTypeVersion = null
                                };

                                var result = await eInvoiceService.SubmitDocument(submitRequest);

                                if (result.IsSuccessful)
                                {
                                    _logger.LogInformation("Successfully created eInvoice for transaction {TransactionNo}", transaction.TrxNo);
                                }
                                else
                                {
                                    _logger.LogWarning("Failed to create eInvoice for transaction {TransactionNo}: {StatusMessage}", transaction.TrxNo, result.StatusMessage);
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error creating eInvoice for transaction {TransactionNo}: {ErrorMessage}", transaction.TrxNo, ex.Message);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error processing transactions for company {CompanyName}: {ErrorMessage}", company.Name, ex.Message);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving companies with auto eInvoice enabled: {ErrorMessage}", ex.Message);
            }
        }
    }
}
