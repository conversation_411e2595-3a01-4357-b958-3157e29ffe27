﻿﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Dolfin.Mobile.API.Models.Request
{
    public class RecurringCreateEInvoiceJobSetting : RecurringJobOption
    {
        /// <summary>
        /// Maximum number of transactions to process in a single batch
        /// </summary>
        public int BatchSize { get; set; } = 10;
        
        /// <summary>
        /// Number of days to look back for transactions that need eInvoices
        /// </summary>
        public int DaysLookback { get; set; } = 7;
    }
}
