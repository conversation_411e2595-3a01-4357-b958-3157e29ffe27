using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Utility.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories.Interfaces
{
    /// <summary>
    /// Repository interface for lookup item operations
    /// </summary>
    public interface ILookupItemRepository : IRepository<LookupItem>
    {
        /// <summary>
        /// Get a lookup item by its code within a specific group
        /// </summary>
        /// <param name="code">The lookup item code</param>
        /// <param name="lookupGroupId">The lookup group ID</param>
        /// <returns>The lookup item if found, null otherwise</returns>
        Task<LookupItem> GetByCodeAsync(string code, Guid lookupGroupId);

        /// <summary>
        /// Get all lookup items for a specific group
        /// </summary>
        /// <param name="lookupGroupId">The lookup group ID</param>
        /// <param name="pagination">Pagination parameters</param>
        /// <param name="filterList">Filter parameters</param>
        /// <returns>A paged list of lookup items</returns>
        Task<PagedList<LookupItem>> GetByGroupAsync(Guid lookupGroupId, Pagination pagination, CommonFilterList filterList);

        /// <summary>
        /// Get all lookup items for a specific group without pagination
        /// </summary>
        /// <param name="lookupGroupId">The lookup group ID</param>
        /// <returns>A list of lookup items</returns>
        Task<IEnumerable<LookupItem>> GetAllByGroupAsync(Guid lookupGroupId);

        /// <summary>
        /// Check if a lookup item code already exists within a specific group
        /// </summary>
        /// <param name="code">The lookup item code</param>
        /// <param name="lookupGroupId">The lookup group ID</param>
        /// <param name="excludeId">Optional ID to exclude from the check (for updates)</param>
        /// <returns>True if the code exists, false otherwise</returns>
        Task<bool> CodeExistsAsync(string code, Guid lookupGroupId, Guid? excludeId = null);
    }
}
