using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories
{
    /// <summary>
    /// Repository implementation for Product entity
    /// </summary>
    public class ProductRepository : BaseRepository<Product>, IProductRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public ProductRepository(
            DolfinDbContext context,
            ICacheService cacheService,
            ILogger<ProductRepository> logger)
            : base(context, cacheService, logger)
        {
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Product>> GetProductsByCompanyAsync(Guid companyId)
        {
            string cacheKey = CacheKeys.Product.GetProductListKey(companyId);

            return await GetOrCreateAsync<IEnumerable<Product>>(
                cacheKey,
                async () => {
                    _logger.LogInformation("Loading products for company {CompanyId} from database", companyId);
                    return await _dbSet
                        .Where(p => p.CompanyId == companyId && p.IsActive)
                        .Include(p => p.ProductCategory)
                        .Include(p => p.ProductUOM)
                        .ToListAsync();
                });
        }

        /// <inheritdoc />
        public async Task<IEnumerable<Product>> GetProductsByCategoryAsync(Guid categoryId)
        {
            return await _dbSet
                .Where(p => p.ProductCategoryId == categoryId && p.IsActive)
                .Include(p => p.ProductUOM)
                .ToListAsync();
        }

        /// <inheritdoc />
        public async Task<Product> GetProductWithDetailsAsync(Guid productId)
        {
            string cacheKey = CacheKeys.Product.GetProductKey(productId);

            return await GetOrCreateAsync<Product>(
                cacheKey,
                async () => {
                    _logger.LogInformation("Loading product {ProductId} with details from database", productId);
                    return await _dbSet
                        .Where(p => p.Id == productId && p.IsActive)
                        .Include(p => p.ProductCategory)
                        .Include(p => p.ProductUOM)
                        .Include(p => p.Company)
                        .Include(p => p.CustomSalesTaxNo)
                        .Include(p => p.CustomServiceTaxNo)
                        .FirstOrDefaultAsync();
                });
        }

        /// <inheritdoc />
        public void InvalidateProductCache(Guid productId)
        {
            string cacheKey = CacheKeys.Product.GetProductKey(productId);
            InvalidateCache(cacheKey);
            _logger.LogInformation("Invalidated cache for product {ProductId}", productId);
        }

        /// <inheritdoc />
        public void InvalidateCompanyProductsCache(Guid companyId)
        {
            string cacheKey = CacheKeys.Product.GetProductListKey(companyId);
            InvalidateCache(cacheKey);
            _logger.LogInformation("Invalidated cache for company {CompanyId} products", companyId);
        }
    }
}
