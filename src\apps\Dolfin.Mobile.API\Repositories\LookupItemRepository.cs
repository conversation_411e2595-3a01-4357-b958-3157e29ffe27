using Dolfin.Framework.Data.Domains;
using Dolfin.Framework.Data.Entity;
using Dolfin.Framework.Repository.Implementations;
using Dolfin.Framework.Repository.Interfaces;
using Dolfin.Mobile.API.Constants;
using Dolfin.Mobile.API.Repositories.Interfaces;
using Dolfin.Utility.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Dolfin.Mobile.API.Repositories
{
    /// <summary>
    /// Repository implementation for lookup item operations
    /// </summary>
    public class LookupItemRepository : BaseRepository<LookupItem>, ILookupItemRepository
    {
        /// <summary>
        /// Constructor
        /// </summary>
        public LookupItemRepository(
            DolfinDbContext context,
            ICacheService cacheService,
            ILogger<LookupItemRepository> logger)
            : base(context, cacheService, logger)
        {
        }

        /// <inheritdoc />
        public async Task<LookupItem> GetByCodeAsync(string code, Guid lookupGroupId)
        {
            return await _dbSet
                .Where(li => li.Code == code && li.LookupGroupId == lookupGroupId && li.IsActive)
                .FirstOrDefaultAsync();
        }

        /// <inheritdoc />
        public async Task<PagedList<LookupItem>> GetByGroupAsync(Guid lookupGroupId, Pagination pagination, CommonFilterList filterList)
        {
            if (pagination == null)
            {
                pagination = new Pagination { PageNumber = 1, PageSize = 10 };
            }

            if (filterList == null)
            {
                filterList = new CommonFilterList();
            }

            // For paged results, we don't use caching to ensure fresh data
            var query = _dbSet.AsQueryable();

            // Apply group filter
            query = query.Where(li => li.LookupGroupId == lookupGroupId && li.IsActive);
            
            // Apply sorting
            //query = ApplySorting(query, filterList);

            // Get total count
            var totalCount = await query.CountAsync();

            // Apply pagination
            var items = await query
                .Skip((pagination.PageNumber - 1) * pagination.PageSize)
                .Take(pagination.PageSize)
                .ToListAsync();

            return new PagedList<LookupItem>(items, totalCount, pagination.PageNumber, pagination.PageSize);
        }

        /// <inheritdoc />
        public async Task<IEnumerable<LookupItem>> GetAllByGroupAsync(Guid lookupGroupId)
        {
            string cacheKey = CacheKeys.Lookup.GetLookupItemListByGroupKey(lookupGroupId);

            return await GetOrCreateAsync<IEnumerable<LookupItem>>(
                cacheKey,
                async () =>
                {
                    _logger.LogInformation("Loading lookup items for group {LookupGroupId} from database", lookupGroupId);
                    return await _dbSet
                        .Where(li => li.LookupGroupId == lookupGroupId && li.IsActive)
                        .OrderBy(li => li.DisplayOrder)
                        .ThenBy(li => li.Value)
                        .ToListAsync();
                });
        }

        /// <inheritdoc />
        public async Task<bool> CodeExistsAsync(string code, Guid lookupGroupId, Guid? excludeId = null)
        {
            var query = _dbSet
                .Where(li => li.Code == code && li.LookupGroupId == lookupGroupId && li.IsActive);

            if (excludeId.HasValue)
            {
                query = query.Where(li => li.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }
    }
}
