using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;
using Dolfin.Mobile.API.Helper;

namespace Dolfin.Mobile.API.Middleware
{
    /// <summary>
    /// Middleware to extract tokens from Authorization header and make them available in the request context
    /// </summary>
    public class TokenExtractionMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<TokenExtractionMiddleware> _logger;

        /// <summary>
        /// Constructor
        /// </summary>
        /// <param name="next">The next middleware in the pipeline</param>
        /// <param name="logger">Logger for the middleware</param>
        public TokenExtractionMiddleware(RequestDelegate next, ILogger<TokenExtractionMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        /// <summary>
        /// Process the request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task InvokeAsync(HttpContext context)
        {
            // Extract tokens from Authorization header or cookies
            var accessToken = Helper.TokenUtils.GetAccessToken(context, _logger);

            // Store the token in the HttpContext.Items collection for later use
            if (!string.IsNullOrEmpty(accessToken))
            {
                context.Items["AccessToken"] = accessToken;
                _logger.LogDebug("Access token stored in HttpContext.Items");
            }

            // Continue with the next middleware in the pipeline
            await _next(context);
        }
    }

    /// <summary>
    /// Extension methods for the TokenExtractionMiddleware
    /// </summary>
    public static class TokenExtractionMiddlewareExtensions
    {
        /// <summary>
        /// Adds the TokenExtractionMiddleware to the application's request pipeline
        /// </summary>
        /// <param name="builder">The application builder</param>
        /// <returns>The application builder</returns>
        public static IApplicationBuilder UseTokenExtraction(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<TokenExtractionMiddleware>();
        }
    }
}
